<%# views/users/komoot_tour_details.ejs %>
<%# Das Layout 'layouts/main_layout' wird verwendet. %>

<%# Seitenspezifische Variablen für das Layout definieren %>
<% pageTitle = locals.pageTitle || 'Komoot-Tour-Details' %>
<% pageSpecificClass = 'komoot-tour-details-page' %>

<!-- Benutzerdefiniertes CSS für die Tour-Details -->
<style>
    .tour-details-table {
        width: 100%;
        margin-bottom: 20px;
    }
    .tour-details-table td {
        padding: 8px 12px;
        vertical-align: top;
    }
    .tour-details-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    .tour-details-label {
        font-weight: bold;
        color: #495057;
        width: 40%;
    }
    .tour-details-value {
        color: #212529;
    }
    .import-options {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
    }
    .import-options h4 {
        margin-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
    }
</style>

<div class="container mt-4">
    <h2><%= typeof pageTitle !== 'undefined' ? pageTitle : 'Komoot-Tour-Details' %></h2>

    <% if (typeof successMessage !== 'undefined' && successMessage) { %>
        <div class="alert alert-success"><%= successMessage %></div>
    <% } %>
    <% if (typeof errorMessage !== 'undefined' && errorMessage) { %>
        <div class="alert alert-danger"><%= errorMessage %></div>
    <% } %>

    <div class="mb-3">
        <div>
            <a href="/user/komoot/tours" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Zurück zur Übersicht
            </a>
            <span class="mx-2">|</span>
            <a href="<%= tour.komoot_url %>" target="_blank" class="btn btn-outline-primary">
                <i class="fas fa-external-link-alt"></i> Auf Komoot öffnen
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2 text-primary"></i>
                        <h3 class="card-title h5 mb-0">Tour-Details</h3>
                    </div>
                </div>
                <div class="card-body">
                    <table class="tour-details-table">
                        <tr>
                            <td class="tour-details-label">Name</td>
                            <td class="tour-details-value"><%= tour.name %></td>
                        </tr>
                        <tr>
                            <td class="tour-details-label">Datum</td>
                            <td class="tour-details-value"><%= new Date(tour.date).toLocaleDateString('de-DE') %></td>
                        </tr>
                        <tr>
                            <td class="tour-details-label">Sportart</td>
                            <td class="tour-details-value">
                                <% let sportIcon = 'fa-hiking';
                                   if (tour.sport_type === 'bike' || tour.sport_type === 'racecycle') sportIcon = 'fa-bicycle';
                                   else if (tour.sport_type === 'mtb') sportIcon = 'fa-biking';
                                   else if (tour.sport_type === 'run') sportIcon = 'fa-running';
                                %>
                                <i class="fas <%= sportIcon %> me-1"></i>
                                <%= tour.sport_type %>
                            </td>
                        </tr>
                        <tr>
                            <td class="tour-details-label">Status</td>
                            <td class="tour-details-value">
                                <% if (tour.status === 'recorded') { %>
                                    <span class="badge bg-success">Aufgezeichnet</span>
                                <% } else { %>
                                    <span class="badge bg-warning text-dark">Geplant</span>
                                <% } %>
                            </td>
                        </tr>
                        <tr>
                            <td class="tour-details-label">Distanz</td>
                            <td class="tour-details-value"><%= (tour.distance / 1000).toFixed(1) %> km</td>
                        </tr>
                        <tr>
                            <td class="tour-details-label">Dauer</td>
                            <td class="tour-details-value"><%= Math.floor(tour.duration / 3600) %>:<%= String(Math.floor((tour.duration % 3600) / 60)).padStart(2, '0') %> h</td>
                        </tr>
                        <tr>
                            <td class="tour-details-label">Höhenmeter (aufwärts)</td>
                            <td class="tour-details-value"><%= tour.elevation_up %> m</td>
                        </tr>
                        <tr>
                            <td class="tour-details-label">Höhenmeter (abwärts)</td>
                            <td class="tour-details-value"><%= tour.elevation_down %> m</td>
                        </tr>
                        <% if (tour.difficulty) { %>
                            <tr>
                                <td class="tour-details-label">Schwierigkeit</td>
                                <td class="tour-details-value"><%= tour.difficulty %></td>
                            </tr>
                        <% } %>
                        <% if (tour.start_point) { %>
                            <tr>
                                <td class="tour-details-label">Startpunkt</td>
                                <td class="tour-details-value"><%= tour.start_point %></td>
                            </tr>
                        <% } %>
                        <% if (tour.end_point) { %>
                            <tr>
                                <td class="tour-details-label">Endpunkt</td>
                                <td class="tour-details-value"><%= tour.end_point %></td>
                            </tr>
                        <% } %>
                        <tr>
                            <td class="tour-details-label">Komoot-ID</td>
                            <td class="tour-details-value"><%= tour.komoot_id %></td>
                        </tr>
                        <tr>
                            <td class="tour-details-label">Synchronisiert am</td>
                            <td class="tour-details-value"><%= new Date(tour.synced_at).toLocaleString('de-DE') %></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <%- include('./tour_preview.ejs', { file: tour, sportTypes: sportTypes }) %>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-file-import me-2 text-primary"></i>
                        <h3 class="card-title h5 mb-0">Route importieren</h3>
                    </div>
                </div>
                <div class="card-body">
                    <form action="/user/komoot/tours/<%= tour.id %>/import" method="POST" id="importForm">
                        <div class="mb-3">
                            <label for="activity_name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="activity_name" name="activity_name" value="<%= tour.name %>" required>
                        </div>
                        <div class="mb-3">
                            <label for="sport_type" class="form-label">Sportart</label>
                            <select class="form-select" id="sport_type" name="sport_type" required>
                                <% sportTypes.forEach(sportType => { %>
                                    <option value="<%= sportType.value %>" <%= tour.sport_type === sportType.value ? 'selected' : '' %>><%= sportType.label %></option>
                                <% }); %>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="activity_type" class="form-label">Aktivitätstyp</label>
                            <select class="form-select" id="activity_type" name="activity_type" required>
                                <option value="planned" <%= tour.status === 'planned' ? 'selected' : '' %>>Geplante Route</option>
                                <option value="activity" <%= tour.status === 'recorded' ? 'selected' : '' %>>Aktivität</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="private_note" class="form-label">Private Notiz</label>
                            <textarea class="form-control" id="private_note" name="private_note" rows="3"></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="extract_elevation" name="extract_elevation" value="1" checked>
                            <label class="form-check-label" for="extract_elevation">
                                Höhendaten aus GPX-Datei extrahieren
                            </label>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="importButton">
                                <i class="fas fa-file-import me-2"></i> Route importieren
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-map-marker-alt me-2 text-success"></i>
                            <h3 class="card-title h5 mb-0">POIs importieren</h3>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="loadPoisButton">
                            <i class="fas fa-search me-1"></i> POIs laden
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="poisLoadingMessage" class="text-center text-muted" style="display: none;">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        Lade POIs aus GPX-Datei...
                    </div>
                    <div id="poisContainer" style="display: none;">
                        <form action="/user/komoot/tours/<%= tour.id %>/import-pois" method="POST" id="poisImportForm">
                            <div id="poisList">
                                <!-- POIs werden hier dynamisch geladen -->
                            </div>
                            <div class="d-grid mt-3" id="poisImportButtonContainer" style="display: none;">
                                <button type="submit" class="btn btn-success" id="poisImportButton">
                                    <i class="fas fa-map-marker-alt me-2"></i> Ausgewählte POIs importieren
                                </button>
                            </div>
                        </form>
                    </div>
                    <div id="poisEmptyMessage" class="text-center text-muted" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        Keine POIs in dieser Tour gefunden.
                    </div>
                    <div id="poisErrorMessage" class="text-center text-danger" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="poisErrorText">Fehler beim Laden der POIs.</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Formular-Handling für den Import-Button -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const importForm = document.getElementById('importForm');
    const importButton = document.getElementById('importButton');
    const loadPoisButton = document.getElementById('loadPoisButton');
    const poisImportForm = document.getElementById('poisImportForm');
    const poisImportButton = document.getElementById('poisImportButton');

    // Route Import Form
    if (importForm) {
        importForm.addEventListener('submit', function(e) {
            importButton.disabled = true;
            importButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Importiere...';
        });
    }

    // POIs laden
    if (loadPoisButton) {
        loadPoisButton.addEventListener('click', function() {
            loadPois();
        });
    }

    // POI Import Form
    if (poisImportForm) {
        poisImportForm.addEventListener('submit', function(e) {
            const selectedPois = document.querySelectorAll('input[name="selected_pois"]:checked');
            if (selectedPois.length === 0) {
                e.preventDefault();
                alert('Bitte wählen Sie mindestens einen POI aus.');
                return;
            }

            poisImportButton.disabled = true;
            poisImportButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Importiere POIs...';
        });
    }

    function loadPois() {
        const tourId = <%= tour.id %>;

        // UI-Elemente
        const loadingMessage = document.getElementById('poisLoadingMessage');
        const poisContainer = document.getElementById('poisContainer');
        const emptyMessage = document.getElementById('poisEmptyMessage');
        const errorMessage = document.getElementById('poisErrorMessage');
        const errorText = document.getElementById('poisErrorText');
        const poisList = document.getElementById('poisList');
        const importButtonContainer = document.getElementById('poisImportButtonContainer');

        // Alle Nachrichten verstecken
        loadingMessage.style.display = 'none';
        poisContainer.style.display = 'none';
        emptyMessage.style.display = 'none';
        errorMessage.style.display = 'none';

        // Loading anzeigen
        loadingMessage.style.display = 'block';
        loadPoisButton.disabled = true;

        // POIs von der API laden
        fetch(`/user/komoot/tours/${tourId}/pois`)
            .then(response => response.json())
            .then(data => {
                loadingMessage.style.display = 'none';
                loadPoisButton.disabled = false;

                if (data.success && data.data && data.data.length > 0) {
                    // POIs anzeigen
                    poisList.innerHTML = '';

                    data.data.forEach((poi, index) => {
                        const poiElement = createPoiElement(poi, index);
                        poisList.appendChild(poiElement);
                    });

                    poisContainer.style.display = 'block';
                    importButtonContainer.style.display = 'block';
                } else {
                    emptyMessage.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error loading POIs:', error);
                loadingMessage.style.display = 'none';
                loadPoisButton.disabled = false;
                errorText.textContent = 'Fehler beim Laden der POIs: ' + error.message;
                errorMessage.style.display = 'block';
            });
    }

    function createPoiElement(poi, index) {
        const div = document.createElement('div');
        div.className = 'form-check mb-3 p-3 border rounded';

        const typeIcon = getPoiTypeIcon(poi.type);
        const shortDescription = poi.description ?
            (poi.description.length > 100 ? poi.description.substring(0, 100) + '...' : poi.description) :
            'Keine Beschreibung verfügbar';

        div.innerHTML = `
            <input class="form-check-input" type="checkbox" name="selected_pois" value="${index}" id="poi_${index}">
            <label class="form-check-label w-100" for="poi_${index}">
                <div class="d-flex align-items-start">
                    <div class="me-3">
                        <i class="fas ${typeIcon} text-primary"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${escapeHtml(poi.name)}</h6>
                        <p class="mb-1 text-muted small">${escapeHtml(shortDescription)}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                ${poi.latitude.toFixed(6)}, ${poi.longitude.toFixed(6)}
                            </small>
                            <span class="badge bg-secondary">${escapeHtml(poi.type)}</span>
                        </div>
                        ${poi.komootUrl ? `
                            <div class="mt-2">
                                <a href="${escapeHtml(poi.komootUrl)}" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-1"></i> Auf Komoot öffnen
                                </a>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </label>
        `;

        return div;
    }

    function getPoiTypeIcon(type) {
        switch (type.toLowerCase()) {
            case 'poi': return 'fa-map-marker-alt';
            case 'highlight': return 'fa-star';
            case 'parking': return 'fa-parking';
            case 'viewpoint': return 'fa-eye';
            default: return 'fa-map-marker-alt';
        }
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
});
</script>

