/**
 * Komoot Routes
 *
 * Diese Routen verwalten die Komoot-Integration.
 */

import express from 'express';
import * as komootController from '../controllers/komootController';
//import { requireLogin } from '../middleware/requireLogin';

const router = express.Router();

// Alle Routen erfordern eine Anmeldung
//router.use(requireLogin);

// Komoot-Einstellungen
router.get('/settings', komootController.showKomootSettings);
router.post('/settings/save', komootController.saveKomootCredentials);
router.post('/settings/delete', komootController.deleteKomootCredentials);

// Komoot-Touren
router.get('/tours', komootController.showKomootTours);
router.get('/sync', komootController.showKomootSync);
router.post('/sync', komootController.syncKomootTours);
router.get('/tours/:id', komootController.showKomootTour);
router.get('/tours/:id/gpx', komootController.getKomootTourGpx);
router.get('/tours/:id/pois', komootController.getKomootTourPois);
router.post('/tours/:id/import', komootController.convertKomootTourToActivity);
router.post('/tours/:id/import-pois', komootController.importKomootTourPois);

export default router;
