/**
 * Kombinierte Geo- und GPX-Hilfsfunktionen
 *
 * Funktionen für geografische Berechnungen und GPX-Datei-Verarbeitung
 */

import fs from 'fs';
import { DOMParser } from 'xmldom';
import logger from './logger';
import polyline from '@mapbox/polyline';
import { GpxData, GpxPoint } from '../types/gpx';
import { KomootPoi } from '../types/komoot';

const log = logger.getLogger(__filename);

// ===== GEOCACHING TYPES =====

export interface GeocachingData {
    name: string;
    description: string;
    latitude: number;
    longitude: number;
    gcCode: string;
    cacheType: string;
    difficulty: number;
    terrain: number;
    container: string;
    placedBy: string;
    url: string;
    shortDescription?: string;
    longDescription?: string;
    hints?: string;
    country?: string;
    state?: string;
    attributes?: string[];
}

export interface GpxAnalysisResult {
    type: 'track' | 'route' | 'waypoints' | 'geocaching';
    pointCount: number;
    hasTimeData: boolean;
    isGeocaching: boolean;
    geocachingData?: GeocachingData;
}

// ===== GEO HELPER FUNCTIONS =====

/**
 * Berechnet Distanz zwischen zwei Koordinaten-Arrays [lon, lat] (Haversine-Formel).
 * Akzeptiert auch Höhenkoordinaten, ignoriert diese aber für die 2D-Distanz.
 * @param coords1 - [lon, lat, ?alt] von Punkt 1.
 * @param coords2 - [lon, lat, ?alt] von Punkt 2.
 * @returns Distanz in Metern.
 */
export function haversineDistance(coords1: number[], coords2: number[]): number {
    if (!coords1 || !coords2 || coords1.length < 2 || coords2.length < 2) {
        log.warn("[haversineDistance] Invalid input coordinates received:", { coords1, coords2 });
        return 0;
    }

    const lon1 = coords1[0];
    const lat1 = coords1[1];
    const lon2 = coords2[0];
    const lat2 = coords2[1];

    if (typeof lat1 !== 'number' || isNaN(lat1) || typeof lon1 !== 'number' || isNaN(lon1) ||
        typeof lat2 !== 'number' || isNaN(lat2) || typeof lon2 !== 'number' || isNaN(lon2)) {
        log.warn("[haversineDistance] Non-numeric coordinate values received:", { lat1, lon1, lat2, lon2 });
        return 0;
    }

    const R = 6371e3; // Erdradius in Metern
    const r = Math.PI / 180; // Umrechnungsfaktor Grad zu Radiant
    const phi1 = lat1 * r;
    const phi2 = lat2 * r;
    const deltaPhi = (lat2 - lat1) * r;
    const deltaLambda = (lon2 - lon1) * r;

    const a = Math.sin(deltaPhi / 2) * Math.sin(deltaPhi / 2) +
              Math.cos(phi1) * Math.cos(phi2) *
              Math.sin(deltaLambda / 2) * Math.sin(deltaLambda / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    const distance = R * c; // Distanz in Metern

    if (isNaN(distance)) {
        log.error("[haversineDistance] Calculation resulted in NaN!", {coords1, coords2, a, c});
        return 0; // Fallback bei NaN
    }
    return distance;
}

/**
 * Berechnet die Steigung in Prozent zwischen zwei Koordinatenpunkten.
 * Benötigt Höheninformationen (Index 2) in den Koordinaten-Arrays.
 * @param c1 - Koordinatenpunkt 1 [lon, lat, alt].
 * @param c2 - Koordinatenpunkt 2 [lon, lat, alt].
 * @returns Die Steigung in Prozent oder 0 bei Fehlern/keiner Distanz/fehlender Höhe.
 */
export function calculateGradient(c1: number[], c2: number[]): number {
    if (!c1 || !c2 || c1.length < 3 || c2.length < 3 ||
        typeof c1[2] !== 'number' || isNaN(c1[2]) ||
        typeof c2[2] !== 'number' || isNaN(c2[2])) {
        // log.debug("[calculateGradient] Invalid input or missing altitude for gradient calculation.");
        return 0;
    }
    const dist = haversineDistance(c1, c2);
    const dAlt = c2[2] - c1[2];
    return (dist > 0.5) ? parseFloat(((dAlt / dist) * 100).toFixed(1)) : 0;
}

// ===== GPX HELPER FUNCTIONS =====

/**
 * Parst eine GPX-Datei und extrahiert Metadaten
 * @param gpxFilePath Pfad zur GPX-Datei
 * @returns GPX-Daten oder null bei Fehler
 */
export function parseGpx(gpxFilePathOrContent: string): GpxData | null {
    const fnLogPrefix = `[GpxUtils ParseGpx]`;

    try {
        // GPX-Datei lesen oder direkt den Inhalt verwenden
        let gpxContent: string;

        // Prüfen, ob es sich um einen Dateipfad oder direkt um GPX-Inhalt handelt
        if (gpxFilePathOrContent.trim().startsWith('<?xml') || gpxFilePathOrContent.trim().startsWith('<gpx')) {
            // Es handelt sich direkt um GPX-Inhalt
            gpxContent = gpxFilePathOrContent;
        } else {
            // Es handelt sich um einen Dateipfad
            gpxContent = fs.readFileSync(gpxFilePathOrContent, 'utf8');
        }

        // XML parsen
        const parser = new DOMParser();
        const gpx = parser.parseFromString(gpxContent, 'text/xml');

        // Ergebnis initialisieren
        const result: GpxData = {
            points: []
        };

        // Name extrahieren (zuerst allgemein, dann aus Track oder Route)
        const nameElements = gpx.getElementsByTagName('name');
        if (nameElements.length > 0) {
            result.name = nameElements[0].textContent || undefined;
        }

        // Falls kein allgemeiner Name gefunden wurde, versuche Track- oder Route-Namen
        if (!result.name) {
            const trackElements = gpx.getElementsByTagName('trk');
            const routeElements = gpx.getElementsByTagName('rte');

            if (trackElements.length > 0) {
                const trackNameElements = trackElements[0].getElementsByTagName('name');
                if (trackNameElements.length > 0) {
                    result.name = trackNameElements[0].textContent || undefined;
                }
            } else if (routeElements.length > 0) {
                const routeNameElements = routeElements[0].getElementsByTagName('name');
                if (routeNameElements.length > 0) {
                    result.name = routeNameElements[0].textContent || undefined;
                }
            }
        }

        // Trackpunkte extrahieren (sowohl trkpt als auch rtept unterstützen)
        const trackPointsCollection = gpx.getElementsByTagName('trkpt');
        const routePointsCollection = gpx.getElementsByTagName('rtept');

        const trackPoints: Element[] = [];
        const gpxPoints: GpxPoint[] = [];

        // Funktion zum Verarbeiten von Punkten (sowohl Track- als auch Route-Punkte)
        const processPoints = (pointsCollection: HTMLCollectionOf<Element>, pointType: string) => {
            for (let i = 0; i < pointsCollection.length; i++) {
                const point = pointsCollection[i];
                trackPoints.push(point);

                // GpxPoint erstellen
                const lat = parseFloat(point.getAttribute('lat') || '0');
                const lng = parseFloat(point.getAttribute('lon') || '0');

                const gpxPoint: GpxPoint = {
                    lat,
                    lng,
                    lon: lng // Für Kompatibilität
                };

                // Höhe hinzufügen, falls vorhanden
                const eleElements = point.getElementsByTagName('ele');
                if (eleElements.length > 0 && eleElements[0].textContent) {
                    gpxPoint.ele = parseFloat(eleElements[0].textContent);
                }

                // Zeit hinzufügen, falls vorhanden
                const timeElements = point.getElementsByTagName('time');
                if (timeElements.length > 0 && timeElements[0].textContent) {
                    gpxPoint.time = new Date(timeElements[0].textContent);
                }

                gpxPoints.push(gpxPoint);
            }
        };

        // Zuerst versuchen, Track-Punkte zu finden
        if (trackPointsCollection.length > 0) {
            log.info(`${fnLogPrefix} Found ${trackPointsCollection.length} track points (trkpt)`);
            processPoints(trackPointsCollection, 'track');
        }
        // Falls keine Track-Punkte vorhanden, versuche Route-Punkte
        else if (routePointsCollection.length > 0) {
            log.info(`${fnLogPrefix} Found ${routePointsCollection.length} route points (rtept), converting to track`);
            processPoints(routePointsCollection, 'route');
        }

        if (trackPoints.length === 0) {
            log.warn(`${fnLogPrefix} No track points (trkpt) or route points (rtept) found in GPX file or content`);
            return result;
        }

        // Punkte zum Ergebnis hinzufügen
        result.points = gpxPoints;

        // Start- und Endpunkt
        const firstPoint = trackPoints[0];
        const lastPoint = trackPoints[trackPoints.length - 1];

        result.startPoint = {
            lat: parseFloat(firstPoint.getAttribute('lat') || '0'),
            lng: parseFloat(firstPoint.getAttribute('lon') || '0')
        };

        result.endPoint = {
            lat: parseFloat(lastPoint.getAttribute('lat') || '0'),
            lng: parseFloat(lastPoint.getAttribute('lon') || '0')
        };

        // Höhendaten extrahieren
        let elevations: number[] = [];
        let prevEle: number | null = null;
        let elevationGain = 0;
        let elevationLoss = 0;

        for (let i = 0; i < trackPoints.length; i++) {
            const eleElements = trackPoints[i].getElementsByTagName('ele');
            if (eleElements.length > 0 && eleElements[0].textContent) {
                const ele = parseFloat(eleElements[0].textContent);
                elevations.push(ele);

                if (prevEle !== null) {
                    const diff = ele - prevEle;
                    if (diff > 0) {
                        elevationGain += diff;
                    } else {
                        elevationLoss += Math.abs(diff);
                    }
                }

                prevEle = ele;
            }
        }

        if (elevations.length > 0) {
            result.elevationMin = Math.min(...elevations);
            result.elevationMax = Math.max(...elevations);
            result.elevationGain = elevationGain;
            result.elevationLoss = elevationLoss;
        }

        // Distanz und Dauer berechnen
        result.distance = calculateDistance(trackPoints);
        result.duration = calculateDuration(trackPoints);

        // Bewegungszeit berechnen (als Schätzung, wenn keine Pausen erkannt werden können)
        result.movingTime = calculateMovingTime(trackPoints) || result.duration;

        // Geschwindigkeit berechnen
        if (result.distance && result.movingTime) {
            result.averageSpeed = result.distance / result.movingTime; // m/s
            result.maxSpeed = calculateMaxSpeed(trackPoints);
        }

        // Herzfrequenz extrahieren (falls vorhanden)
        const heartrates = extractHeartrates(trackPoints);
        if (heartrates.length > 0) {
            result.averageHeartrate = calculateAverage(heartrates);
            result.maxHeartrate = Math.max(...heartrates);
        }

        // Polylines und Bounds generieren
        if (trackPoints.length > 0) {
            const coordinates = trackPoints.map(point => {
                const lat = parseFloat(point.getAttribute('lat') || '0');
                const lng = parseFloat(point.getAttribute('lon') || '0');
                return [lat, lng] as [number, number];
            });

            // Detaillierte Polyline (alle Punkte)
            result.detailedPolyline = polyline.encode(coordinates);

            // Summary Polyline (reduzierte Anzahl von Punkten)
            const simplifiedCoordinates = simplifyCoordinates(coordinates);
            result.summaryPolyline = polyline.encode(simplifiedCoordinates);

            // Stream JSON generieren
            result.streamJson = generateStreamJson(trackPoints);

            // Bounds berechnen
            if (coordinates.length > 0) {
                const lats = coordinates.map(coord => coord[0]);
                const lngs = coordinates.map(coord => coord[1]);

                result.bounds = {
                    maxLat: Math.max(...lats),
                    minLat: Math.min(...lats),
                    maxLng: Math.max(...lngs),
                    minLng: Math.min(...lngs)
                };
            }
        }

        return result;
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to parse GPX file or content: ${error}`);
        return null;
    }
}

// ===== INTERNAL GPX HELPER FUNCTIONS =====

/**
 * Berechnet die Distanz zwischen zwei Punkten mit lat/lon Attributen (für GPX-Elemente)
 * @param lat1 Breitengrad des ersten Punkts
 * @param lon1 Längengrad des ersten Punkts
 * @param lat2 Breitengrad des zweiten Punkts
 * @param lon2 Längengrad des zweiten Punkts
 * @returns Distanz in Metern
 */
function haversineDistanceLatLon(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Erdradius in Metern
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
}

/**
 * Berechnet die Gesamtdistanz einer GPX-Strecke
 * @param trackPoints Trackpunkte aus der GPX-Datei
 * @returns Distanz in Metern
 */
function calculateDistance(trackPoints: Element[]): number {
    let distance = 0;

    for (let i = 0; i < trackPoints.length - 1; i++) {
        const p1 = trackPoints[i];
        const p2 = trackPoints[i + 1];

        const lat1 = parseFloat(p1.getAttribute('lat') || '0');
        const lon1 = parseFloat(p1.getAttribute('lon') || '0');
        const lat2 = parseFloat(p2.getAttribute('lat') || '0');
        const lon2 = parseFloat(p2.getAttribute('lon') || '0');

        distance += haversineDistanceLatLon(lat1, lon1, lat2, lon2);
    }

    return distance;
}

/**
 * Berechnet die Dauer einer GPX-Strecke
 * @param trackPoints Trackpunkte aus der GPX-Datei
 * @returns Dauer in Sekunden oder 0, wenn keine Zeitstempel vorhanden sind
 */
function calculateDuration(trackPoints: Element[]): number {
    if (trackPoints.length < 2) {
        return 0;
    }

    const firstPoint = trackPoints[0];
    const lastPoint = trackPoints[trackPoints.length - 1];

    const firstTimeElements = firstPoint.getElementsByTagName('time');
    const lastTimeElements = lastPoint.getElementsByTagName('time');

    if (firstTimeElements.length === 0 || lastTimeElements.length === 0) {
        return 0;
    }

    const firstTime = new Date(firstTimeElements[0].textContent || '').getTime();
    const lastTime = new Date(lastTimeElements[0].textContent || '').getTime();

    if (isNaN(firstTime) || isNaN(lastTime)) {
        return 0;
    }

    return (lastTime - firstTime) / 1000; // Umrechnung von Millisekunden in Sekunden
}

/**
 * Berechnet die Bewegungszeit einer GPX-Strecke (ohne Pausen)
 * @param trackPoints Trackpunkte aus der GPX-Datei
 * @returns Bewegungszeit in Sekunden oder 0, wenn keine Zeitstempel vorhanden sind
 */
function calculateMovingTime(trackPoints: Element[]): number {
    if (trackPoints.length < 2) {
        return 0;
    }

    let movingTime = 0;
    let prevPoint: Element | null = null;
    let prevTime: number | null = null;

    for (let i = 0; i < trackPoints.length; i++) {
        const point = trackPoints[i];
        const timeElements = point.getElementsByTagName('time');

        if (timeElements.length === 0 || !timeElements[0].textContent) {
            continue;
        }

        const time = new Date(timeElements[0].textContent).getTime();
        if (isNaN(time)) {
            continue;
        }

        if (prevPoint !== null && prevTime !== null) {
            const timeDiff = (time - prevTime) / 1000; // in Sekunden

            // Berechne die Distanz zwischen den Punkten
            const lat1 = parseFloat(prevPoint.getAttribute('lat') || '0');
            const lon1 = parseFloat(prevPoint.getAttribute('lon') || '0');
            const lat2 = parseFloat(point.getAttribute('lat') || '0');
            const lon2 = parseFloat(point.getAttribute('lon') || '0');

            const distance = haversineDistanceLatLon(lat1, lon1, lat2, lon2);

            // Berechne die Geschwindigkeit in m/s
            const speed = distance / timeDiff;

            // Wenn die Geschwindigkeit über einem Schwellenwert liegt, zähle die Zeit als Bewegungszeit
            // Typischer Schwellenwert: 0.3 m/s (ca. 1 km/h)
            if (speed > 0.3) {
                movingTime += timeDiff;
            }
        }

        prevPoint = point;
        prevTime = time;
    }

    return movingTime;
}

/**
 * Berechnet die maximale Geschwindigkeit einer GPX-Strecke
 * @param trackPoints Trackpunkte aus der GPX-Datei
 * @returns Maximale Geschwindigkeit in m/s oder 0, wenn keine Zeitstempel vorhanden sind
 */
function calculateMaxSpeed(trackPoints: Element[]): number {
    if (trackPoints.length < 2) {
        return 0;
    }

    let maxSpeed = 0;
    let prevPoint: Element | null = null;
    let prevTime: number | null = null;

    for (let i = 0; i < trackPoints.length; i++) {
        const point = trackPoints[i];
        const timeElements = point.getElementsByTagName('time');

        if (timeElements.length === 0 || !timeElements[0].textContent) {
            continue;
        }

        const time = new Date(timeElements[0].textContent).getTime();
        if (isNaN(time)) {
            continue;
        }

        if (prevPoint !== null && prevTime !== null) {
            const timeDiff = (time - prevTime) / 1000; // in Sekunden

            // Berechne die Distanz zwischen den Punkten
            const lat1 = parseFloat(prevPoint.getAttribute('lat') || '0');
            const lon1 = parseFloat(prevPoint.getAttribute('lon') || '0');
            const lat2 = parseFloat(point.getAttribute('lat') || '0');
            const lon2 = parseFloat(point.getAttribute('lon') || '0');

            const distance = haversineDistanceLatLon(lat1, lon1, lat2, lon2);

            // Berechne die Geschwindigkeit in m/s
            const speed = distance / timeDiff;

            // Ignoriere unrealistische Geschwindigkeiten (z.B. GPS-Fehler)
            // Typischer Maximalwert: 30 m/s (ca. 108 km/h)
            if (speed > 0 && speed < 30) {
                maxSpeed = Math.max(maxSpeed, speed);
            }
        }

        prevPoint = point;
        prevTime = time;
    }

    return maxSpeed;
}

/**
 * Extrahiert Herzfrequenzdaten aus den Trackpunkten
 * @param trackPoints Trackpunkte aus der GPX-Datei
 * @returns Array mit Herzfrequenzwerten
 */
function extractHeartrates(trackPoints: Element[]): number[] {
    const heartrates: number[] = [];

    for (let i = 0; i < trackPoints.length; i++) {
        const point = trackPoints[i];
        const extensionsElements = point.getElementsByTagName('extensions');

        if (extensionsElements.length > 0) {
            const hrElements = extensionsElements[0].getElementsByTagName('gpxtpx:hr');

            if (hrElements.length === 0) {
                // Versuche alternative Tags
                const altHrElements = extensionsElements[0].getElementsByTagName('hr');
                if (altHrElements.length > 0 && altHrElements[0].textContent) {
                    const hr = parseFloat(altHrElements[0].textContent);
                    if (!isNaN(hr)) {
                        heartrates.push(hr);
                    }
                }
            } else if (hrElements[0].textContent) {
                const hr = parseFloat(hrElements[0].textContent);
                if (!isNaN(hr)) {
                    heartrates.push(hr);
                }
            }
        }
    }

    return heartrates;
}

/**
 * Berechnet den Durchschnitt eines Arrays von Zahlen
 * @param values Array von Zahlen
 * @returns Durchschnitt oder 0, wenn das Array leer ist
 */
function calculateAverage(values: number[]): number {
    if (values.length === 0) {
        return 0;
    }

    const sum = values.reduce((a, b) => a + b, 0);
    return sum / values.length;
}

/**
 * Vereinfacht ein Array von Koordinaten für die Summary Polyline
 * @param coordinates Array von Koordinaten [lat, lng]
 * @returns Vereinfachtes Array von Koordinaten
 */
function simplifyCoordinates(coordinates: [number, number][]): [number, number][] {
    if (coordinates.length <= 2) {
        return coordinates;
    }

    // Für eine einfache Vereinfachung: Nehme jeden n-ten Punkt
    const n = Math.max(1, Math.floor(coordinates.length / 100)); // Max. 100 Punkte

    const simplified: [number, number][] = [];
    simplified.push(coordinates[0]); // Startpunkt immer hinzufügen

    for (let i = n; i < coordinates.length - 1; i += n) {
        simplified.push(coordinates[i]);
    }

    simplified.push(coordinates[coordinates.length - 1]); // Endpunkt immer hinzufügen

    return simplified;
}

/**
 * Generiert ein Stream-JSON aus den Trackpunkten
 * @param trackPoints Trackpunkte aus der GPX-Datei
 * @returns Stream-JSON als String
 */
function generateStreamJson(trackPoints: Element[]): string {
    const latlng: number[][] = [];
    const altitude: number[] = [];
    const time: string[] = [];
    const heartrate: number[] = [];

    for (let i = 0; i < trackPoints.length; i++) {
        const point = trackPoints[i];

        // Koordinaten
        const lat = parseFloat(point.getAttribute('lat') || '0');
        const lng = parseFloat(point.getAttribute('lon') || '0');
        if (!isNaN(lat) && !isNaN(lng)) {
            latlng.push([lat, lng]);
        }

        // Höhe
        const eleElements = point.getElementsByTagName('ele');
        if (eleElements.length > 0 && eleElements[0].textContent) {
            const ele = parseFloat(eleElements[0].textContent);
            if (!isNaN(ele)) {
                altitude.push(ele);
            } else {
                altitude.push(0);
            }
        } else {
            altitude.push(0);
        }

        // Zeit
        const timeElements = point.getElementsByTagName('time');
        if (timeElements.length > 0 && timeElements[0].textContent) {
            time.push(timeElements[0].textContent);
        } else {
            time.push('');
        }

        // Herzfrequenz
        const extensionsElements = point.getElementsByTagName('extensions');
        let hr = 0;

        if (extensionsElements.length > 0) {
            const hrElements = extensionsElements[0].getElementsByTagName('gpxtpx:hr');

            if (hrElements.length === 0) {
                // Versuche alternative Tags
                const altHrElements = extensionsElements[0].getElementsByTagName('hr');
                if (altHrElements.length > 0 && altHrElements[0].textContent) {
                    hr = parseFloat(altHrElements[0].textContent) || 0;
                }
            } else if (hrElements[0].textContent) {
                hr = parseFloat(hrElements[0].textContent) || 0;
            }
        }

        heartrate.push(hr);
    }

    const streams = [
        {
            type: 'latlng',
            data: latlng
        },
        {
            type: 'altitude',
            data: altitude
        },
        {
            type: 'time',
            data: time
        }
    ];

    // Füge Herzfrequenz nur hinzu, wenn Daten vorhanden sind
    if (heartrate.some(hr => hr > 0)) {
        streams.push({
            type: 'heartrate',
            data: heartrate
        });
    }

    return JSON.stringify(streams);
}

// ===== POI/WAYPOINT HELPER FUNCTIONS =====

/**
 * Extrahiert POIs/Wegpunkte aus einer GPX-Datei
 * @param gpxFilePathOrContent Pfad zur GPX-Datei oder GPX-Inhalt
 * @returns Array von POI-Objekten
 */
export function extractPoisFromGpx(gpxFilePathOrContent: string): KomootPoi[] {
    const fnLogPrefix = `[GpxUtils ExtractPoisFromGpx]`;

    try {
        // GPX-Datei lesen oder direkt den Inhalt verwenden
        let gpxContent: string;

        if (gpxFilePathOrContent.trim().startsWith('<?xml') || gpxFilePathOrContent.trim().startsWith('<gpx')) {
            gpxContent = gpxFilePathOrContent;
        } else {
            gpxContent = fs.readFileSync(gpxFilePathOrContent, 'utf8');
        }

        // XML parsen
        const parser = new DOMParser();
        const gpx = parser.parseFromString(gpxContent, 'text/xml');

        // Alle Wegpunkte finden
        const waypoints = gpx.getElementsByTagName('wpt');
        const pois: KomootPoi[] = [];

        for (let i = 0; i < waypoints.length; i++) {
            const wpt = waypoints[i];

            // Koordinaten extrahieren
            const lat = parseFloat(wpt.getAttribute('lat') || '0');
            const lng = parseFloat(wpt.getAttribute('lon') || '0');

            if (lat === 0 || lng === 0) continue;

            // POI-Daten extrahieren
            const nameElement = wpt.getElementsByTagName('name')[0];
            const descElement = wpt.getElementsByTagName('desc')[0];
            const typeElement = wpt.getElementsByTagName('type')[0];
            const srcElement = wpt.getElementsByTagName('src')[0];
            const linkElement = wpt.getElementsByTagName('link')[0];
            const cmtElement = wpt.getElementsByTagName('cmt')[0];

            const name = nameElement?.textContent?.trim() || '';
            const description = descElement?.textContent?.trim() || '';
            const type = typeElement?.textContent?.trim() || 'POI';
            const source = srcElement?.textContent?.trim() || 'Komoot';
            const komootUrl = linkElement?.getAttribute('href') || '';
            const imageUrl = cmtElement?.textContent?.trim() || '';

            // Nur POIs mit Namen hinzufügen
            if (name) {
                pois.push({
                    name,
                    description,
                    latitude: lat,
                    longitude: lng,
                    type,
                    source,
                    komootUrl,
                    imageUrl: imageUrl.startsWith('http') ? imageUrl : ''
                });
            }
        }

        log.info(`${fnLogPrefix} Extracted ${pois.length} POIs from GPX`);
        return pois;

    } catch (error) {
        log.error(`${fnLogPrefix} Error extracting POIs from GPX:`, error);
        return [];
    }
}

// ===== GEOCACHING HELPER FUNCTIONS =====

/**
 * Analysiert eine GPX-Datei und bestimmt den Typ (Track, Route, Waypoints, Geocaching)
 * @param gpxFilePathOrContent Pfad zur GPX-Datei oder GPX-Inhalt
 * @returns Analyse-Ergebnis
 */
export function analyzeGpx(gpxFilePathOrContent: string): GpxAnalysisResult | null {
    const fnLogPrefix = `[GpxUtils AnalyzeGpx]`;

    try {
        // GPX-Datei lesen oder direkt den Inhalt verwenden
        let gpxContent: string;

        if (gpxFilePathOrContent.trim().startsWith('<?xml') || gpxFilePathOrContent.trim().startsWith('<gpx')) {
            gpxContent = gpxFilePathOrContent;
        } else {
            gpxContent = fs.readFileSync(gpxFilePathOrContent, 'utf8');
        }

        // XML parsen
        const parser = new DOMParser();
        const gpx = parser.parseFromString(gpxContent, 'text/xml');

        // Zähle verschiedene Punkt-Typen
        const trackPoints = gpx.getElementsByTagName('trkpt');
        const routePoints = gpx.getElementsByTagName('rtept');
        const waypoints = gpx.getElementsByTagName('wpt');

        // Prüfe auf Geocaching-spezifische Elemente
        const groundspeakCache = gpx.getElementsByTagName('groundspeak:cache');
        const isGeocaching = groundspeakCache.length > 0;

        let result: GpxAnalysisResult = {
            type: 'waypoints',
            pointCount: 0,
            hasTimeData: false,
            isGeocaching: false
        };

        // Bestimme den Typ basierend auf vorhandenen Punkten
        if (trackPoints.length > 0) {
            result.type = 'track';
            result.pointCount = trackPoints.length;

            // Prüfe auf Zeitdaten in Track-Punkten
            for (let i = 0; i < Math.min(5, trackPoints.length); i++) {
                const timeElements = trackPoints[i].getElementsByTagName('time');
                if (timeElements.length > 0 && timeElements[0].textContent) {
                    result.hasTimeData = true;
                    break;
                }
            }
        } else if (routePoints.length > 0) {
            result.type = 'route';
            result.pointCount = routePoints.length;
        } else if (waypoints.length > 0) {
            result.type = 'waypoints';
            result.pointCount = waypoints.length;
        }

        // Geocaching-spezifische Behandlung
        if (isGeocaching && waypoints.length > 0) {
            result.type = 'geocaching';
            result.isGeocaching = true;

            // Extrahiere Geocaching-Daten
            const geocachingData = parseGeocachingData(gpx);
            if (geocachingData) {
                result.geocachingData = geocachingData;
            }
        }

        log.info(`${fnLogPrefix} Analysis result: type=${result.type}, points=${result.pointCount}, geocaching=${result.isGeocaching}`);
        return result;

    } catch (error) {
        log.error(`${fnLogPrefix} Failed to analyze GPX: ${error}`);
        return null;
    }
}

/**
 * Parst Geocaching-spezifische Daten aus einer GPX-Datei
 * @param gpx Geparste GPX-DOM
 * @returns Geocaching-Daten oder null
 */
export function parseGeocachingData(gpx: Document): GeocachingData | null {
    const fnLogPrefix = `[GpxUtils ParseGeocachingData]`;

    try {
        // Finde den ersten Waypoint
        const waypoints = gpx.getElementsByTagName('wpt');
        if (waypoints.length === 0) {
            log.warn(`${fnLogPrefix} No waypoints found`);
            return null;
        }

        const waypoint = waypoints[0];
        const latitude = parseFloat(waypoint.getAttribute('lat') || '0');
        const longitude = parseFloat(waypoint.getAttribute('lon') || '0');

        // Basis-Informationen aus dem Waypoint
        const nameElements = waypoint.getElementsByTagName('name');
        const descElements = waypoint.getElementsByTagName('desc');
        const urlElements = waypoint.getElementsByTagName('url');

        const gcCode = nameElements.length > 0 ? nameElements[0].textContent || '' : '';
        const description = descElements.length > 0 ? descElements[0].textContent || '' : '';
        const url = urlElements.length > 0 ? urlElements[0].textContent || '' : '';

        // Groundspeak-spezifische Daten
        const groundspeakCache = gpx.getElementsByTagName('groundspeak:cache');
        if (groundspeakCache.length === 0) {
            log.warn(`${fnLogPrefix} No groundspeak:cache element found`);
            return null;
        }

        const cache = groundspeakCache[0];

        // Cache-Name
        const cacheNameElements = cache.getElementsByTagName('groundspeak:name');
        const cacheName = cacheNameElements.length > 0 ? cacheNameElements[0].textContent || gcCode : gcCode;

        // Cache-Typ
        const cacheTypeElements = cache.getElementsByTagName('groundspeak:type');
        const cacheType = cacheTypeElements.length > 0 ? cacheTypeElements[0].textContent || 'Unknown' : 'Unknown';

        // Schwierigkeit und Gelände
        const difficultyElements = cache.getElementsByTagName('groundspeak:difficulty');
        const terrainElements = cache.getElementsByTagName('groundspeak:terrain');
        const difficulty = difficultyElements.length > 0 ? parseFloat(difficultyElements[0].textContent || '0') : 0;
        const terrain = terrainElements.length > 0 ? parseFloat(terrainElements[0].textContent || '0') : 0;

        // Container-Größe
        const containerElements = cache.getElementsByTagName('groundspeak:container');
        const container = containerElements.length > 0 ? containerElements[0].textContent || 'Unknown' : 'Unknown';

        // Versteckt von
        const placedByElements = cache.getElementsByTagName('groundspeak:placed_by');
        const placedBy = placedByElements.length > 0 ? placedByElements[0].textContent || 'Unknown' : 'Unknown';

        // Land und Staat
        const countryElements = cache.getElementsByTagName('groundspeak:country');
        const stateElements = cache.getElementsByTagName('groundspeak:state');
        const country = countryElements.length > 0 ? countryElements[0].textContent || undefined : undefined;
        const state = stateElements.length > 0 ? stateElements[0].textContent || undefined : undefined;

        // Beschreibungen
        const shortDescElements = cache.getElementsByTagName('groundspeak:short_description');
        const longDescElements = cache.getElementsByTagName('groundspeak:long_description');
        const shortDescription = shortDescElements.length > 0 ? shortDescElements[0].textContent || undefined : undefined;
        const longDescription = longDescElements.length > 0 ? longDescElements[0].textContent || undefined : undefined;

        // Hinweise
        const hintsElements = cache.getElementsByTagName('groundspeak:encoded_hints');
        const hints = hintsElements.length > 0 ? hintsElements[0].textContent || undefined : undefined;

        // Attribute extrahieren
        const attributeElements = cache.getElementsByTagName('groundspeak:attribute');
        const attributes: string[] = [];
        for (let i = 0; i < attributeElements.length; i++) {
            const attr = attributeElements[i];
            const id = attr.getAttribute('id');
            const inc = attr.getAttribute('inc');
            const text = attr.textContent;
            if (text) {
                attributes.push(`${inc === '1' ? '+' : '-'}${text}`);
            }
        }

        // Erstelle formatierte Beschreibung
        let formattedDescription = `**${cacheName}** (${gcCode})\n\n`;
        formattedDescription += `**Typ:** ${mapCacheTypeToGerman(cacheType)}\n`;
        formattedDescription += `**Schwierigkeit:** ${difficulty}/5 | **Gelände:** ${terrain}/5\n`;
        formattedDescription += `**Container:** ${container}\n`;
        formattedDescription += `**Versteckt von:** ${placedBy}\n`;

        if (country || state) {
            formattedDescription += `**Ort:** ${state ? state + ', ' : ''}${country || ''}\n`;
        }

        if (shortDescription) {
            formattedDescription += `\n**Kurzbeschreibung:**\n${cleanHtmlDescription(shortDescription)}\n`;
        }

        if (longDescription) {
            formattedDescription += `\n**Beschreibung:**\n${cleanHtmlDescription(longDescription)}\n`;
        }

        if (hints) {
            formattedDescription += `\n**Hinweise:** ${hints}\n`;
        }

        if (attributes.length > 0) {
            formattedDescription += `\n**Attribute:** ${attributes.join(', ')}\n`;
        }

        const geocachingData: GeocachingData = {
            name: cacheName,
            description: formattedDescription,
            latitude,
            longitude,
            gcCode,
            cacheType,
            difficulty,
            terrain,
            container,
            placedBy,
            url,
            shortDescription,
            longDescription,
            hints,
            country,
            state,
            attributes
        };

        log.info(`${fnLogPrefix} Successfully parsed geocaching data for ${gcCode}`);
        return geocachingData;

    } catch (error) {
        log.error(`${fnLogPrefix} Failed to parse geocaching data: ${error}`);
        return null;
    }
}

/**
 * Mappt Geocaching-Cache-Typen zu deutschen POI-Typen
 * @param cacheType Geocaching-Cache-Typ
 * @returns POI-Typ für die Datenbank
 */
export function mapCacheTypeToPOIType(cacheType: string): string {
    const typeMap: { [key: string]: string } = {
        'Traditional Cache': 'traditional_cache',
        'Multi-cache': 'multi_cache',
        'Mystery Cache': 'mystery_cache',
        'Puzzle Cache': 'mystery_cache',
        'Letterbox Hybrid': 'letterbox_cache',
        'EarthCache': 'earthcache',
        'Wherigo Cache': 'wherigo_cache',
        'Event Cache': 'event_cache',
        'Mega-Event Cache': 'mega_event',
        'Giga-Event Cache': 'giga_event',
        'Cache In Trash Out Event': 'cito_event',
        'Virtual Cache': 'virtual_cache',
        'Webcam Cache': 'webcam_cache'
    };

    return typeMap[cacheType] || 'unknown_cache';
}

/**
 * Mappt Geocaching-Cache-Typen zu deutschen Bezeichnungen
 * @param cacheType Geocaching-Cache-Typ
 * @returns Deutsche Bezeichnung
 */
function mapCacheTypeToGerman(cacheType: string): string {
    const typeMap: { [key: string]: string } = {
        'Traditional Cache': 'Traditional Cache',
        'Multi-cache': 'Multi-Cache',
        'Mystery Cache': 'Mystery/Puzzle Cache',
        'Puzzle Cache': 'Mystery/Puzzle Cache',
        'Letterbox Hybrid': 'Letterbox Hybrid',
        'EarthCache': 'EarthCache',
        'Wherigo Cache': 'Wherigo Cache',
        'Event Cache': 'Event Cache',
        'Mega-Event Cache': 'Mega-Event Cache',
        'Giga-Event Cache': 'Giga-Event Cache',
        'Cache In Trash Out Event': 'CITO Event',
        'Virtual Cache': 'Virtual Cache',
        'Webcam Cache': 'Webcam Cache'
    };

    return typeMap[cacheType] || cacheType;
}

/**
 * Bereinigt HTML-Beschreibungen von Geocaches
 * @param htmlDescription HTML-Beschreibung
 * @returns Bereinigte Text-Beschreibung
 */
function cleanHtmlDescription(htmlDescription: string): string {
    if (!htmlDescription) return '';

    // Entferne HTML-Tags und konvertiere HTML-Entities
    let cleaned = htmlDescription
        .replace(/<[^>]*>/g, '') // Entferne HTML-Tags
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&nbsp;/g, ' ')
        .trim();

    // Entferne übermäßige Leerzeichen und Zeilenumbrüche
    cleaned = cleaned.replace(/\s+/g, ' ').trim();

    // Begrenze die Länge
    if (cleaned.length > 500) {
        cleaned = cleaned.substring(0, 497) + '...';
    }

    return cleaned;
}
