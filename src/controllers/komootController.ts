/**
 * Komoot Controller
 *
 * Dieser Controller verwaltet die Komoot-Integration.
 */

import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import komootService from '../services/komootService';
import { KomootCredentials, KomootSyncOptions } from '../types/komoot';
import * as sportTypeRepository from '../db/sportTypeRepository';

const log = logger.getLogger(__filename);

/**
 * Zeigt die Komoot-Einstellungsseite an
 */
export const showKomootSettings = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const loggedInUserId = res.locals.currentUser.id;
    const activityDbId = parseInt(req.params.activityDbId, 10); // Dies IST die interne DB ID der Aktivität
    const fnLogPrefix = `[KomootCtrl ShowSettings User:${loggedInUserId} ActDB:${activityDbId}]]`;

    try {
        // Anmeldeinformationen holen
        const credentials = await komootService.getCredentials(loggedInUserId);

        // Seite rendern
        res.render('users/komoot_settings', {
            pageTitle: 'Komoot-Einstellungen',
            hasCredentials: !!credentials,
            komootEmail: credentials?.email || ''
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to show Komoot settings: ${error}`);
        next(error);
    }
};

/**
 * Speichert die Komoot-Anmeldeinformationen
 */
export const saveKomootCredentials = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const fnLogPrefix = `[KomootCtrl SaveCreds User:${req.user?.id}]`;

    try {
        const { email, password } = req.body;

        // Validierung
        if (!email || !password) {
            req.flash('error', 'E-Mail-Adresse und Passwort sind erforderlich');
            return res.redirect('/user/komoot/settings');
        }

        // Anmeldeinformationen speichern
        const credentials: KomootCredentials = {
            email,
            password
        };

        const success = await komootService.saveCredentials(req.user!.id, credentials);

        if (success) {
            req.flash('success', 'Komoot-Anmeldeinformationen erfolgreich gespeichert');
        } else {
            req.flash('error', 'Fehler beim Speichern der Komoot-Anmeldeinformationen. Bitte überprüfen Sie Ihre Eingaben.');
        }

        res.redirect('/user/komoot/settings');
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to save Komoot credentials: ${error}`);
        req.flash('error', 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        res.redirect('/user/komoot/settings');
    }
};

/**
 * Löscht die Komoot-Anmeldeinformationen
 */
export const deleteKomootCredentials = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const fnLogPrefix = `[KomootCtrl DeleteCreds User:${req.user?.id}]`;

    try {
        const success = await komootService.deleteCredentials(req.user!.id);

        if (success) {
            req.flash('success', 'Komoot-Anmeldeinformationen erfolgreich gelöscht');
        } else {
            req.flash('error', 'Fehler beim Löschen der Komoot-Anmeldeinformationen');
        }

        res.redirect('/user/komoot/settings');
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to delete Komoot credentials: ${error}`);
        req.flash('error', 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        res.redirect('/user/komoot/settings');
    }
};

/**
 * Zeigt die Komoot-Touren-Übersicht an
 */
export const showKomootTours = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const fnLogPrefix = `[KomootCtrl ShowTours User:${req.user?.id}]`;

    try {
        // Anmeldeinformationen holen
        const credentials = await komootService.getCredentials(req.user!.id);

        if (!credentials) {
            req.flash('error', 'Keine Komoot-Anmeldeinformationen gefunden. Bitte konfigurieren Sie Ihre Komoot-Einstellungen.');
            return res.redirect('/user/komoot/settings');
        }

        // Touren holen
        const tours = await komootService.getTours(req.user!.id);

        // Wir brauchen die convertedTours Liste nicht mehr, da wir jetzt das is_imported Flag direkt in der Tour haben
        // Behalten wir sie aber für Abwärtskompatibilität bei
        const convertedTours = await komootService.getConvertedTours(req.user!.id);

        // Flash-Nachrichten holen
        const successMessage = req.flash('success') as string[];
        const errorMessage = req.flash('error') as string[];

        // Debug-Ausgabe
        if (successMessage && successMessage.length > 0) {
            log.info(`${fnLogPrefix} Success message: ${successMessage}`);
        }
        if (errorMessage && errorMessage.length > 0) {
            log.info(`${fnLogPrefix} Error message: ${errorMessage}`);
        }

        // Seite rendern
        res.render('users/komoot_tours', {
            pageTitle: 'Komoot-Touren',
            tours,
            convertedTours,
            successMessage: successMessage.length > 0 ? successMessage[0] : null,
            errorMessage: errorMessage.length > 0 ? errorMessage[0] : null
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to show Komoot tours: ${error}`);
        next(error);
    }
};

/**
 * Synchronisiert die Komoot-Touren
 */
export const syncKomootTours = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
    const fnLogPrefix = `[KomootCtrl SyncTours User:${req.user?.id}]`;

    try {
        // Anmeldeinformationen holen
        const credentials = await komootService.getCredentials(req.user!.id);

        if (!credentials) {
            req.flash('error', 'Keine Komoot-Anmeldeinformationen gefunden. Bitte konfigurieren Sie Ihre Komoot-Einstellungen.');
            return res.redirect('/user/komoot/settings');
        }

        // Synchronisierungsoptionen
        const options: KomootSyncOptions = {
            userId: req.user!.id
        };

        // Sportarten filtern
        if (req.body.sportTypes && Array.isArray(req.body.sportTypes)) {
            options.sportTypes = req.body.sportTypes;
        }

        // Status filtern
        if (req.body.status) {
            options.status = req.body.status;
        }

        // Datumsbereich filtern
        if (req.body.fromDate) {
            options.fromDate = new Date(req.body.fromDate);
        }

        if (req.body.toDate) {
            options.toDate = new Date(req.body.toDate);
        }

        // Limit
        if (req.body.limit) {
            options.limit = parseInt(req.body.limit, 10);
        }

        // Touren synchronisieren
        const result = await komootService.syncTours(options);

        // Erfolg/Fehler anzeigen
        let message = `Synchronisierung abgeschlossen: ${result.newTours} neue Touren`;

        if (result.updatedTours > 0) {
            message += `, ${result.updatedTours} aktualisierte Touren`;
        }

        if (result.skippedTours > 0) {
            message += `, ${result.skippedTours} übersprungene Touren`;
        }

        if (result.failedTours > 0) {
            message += `, ${result.failedTours} fehlgeschlagene Touren`;
            log.warn(`${fnLogPrefix} ${result.failedTours} tours failed to sync`);
        }

        log.debug(`${fnLogPrefix} Setting flash message: ${message}`);

        // Prüfen, ob es sich um eine AJAX-Anfrage handelt
        if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
            // AJAX-Antwort senden
            return res.json({
                status: 'success',
                message,
                result
            });
        }

        // Flash-Nachricht setzen
        req.flash('success', message);

        // Weiterleitung zur Touren-Seite
        res.redirect('/user/komoot/tours');
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to sync Komoot tours: ${error}`);
        req.flash('error', 'Ein Fehler ist bei der Synchronisierung aufgetreten. Bitte versuchen Sie es später erneut.');
        res.redirect('/user/komoot/tours');
    }
};

/**
 * Zeigt die Details einer Komoot-Tour an
 */
export const showKomootTour = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const fnLogPrefix = `[KomootCtrl ShowTour User:${req.user?.id} TourID:${req.params.id}]`;

    try {
        const tourId = parseInt(req.params.id, 10);

        if (isNaN(tourId)) {
            req.flash('error', 'Ungültige Tour-ID');
            return res.redirect('/user/komoot/tours');
        }

        // Tour holen
        const tour = await komootService.getTour(tourId);

        if (!tour) {
            req.flash('error', 'Tour nicht gefunden');
            return res.redirect('/user/komoot/tours');
        }

        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user!.id) {
            req.flash('error', 'Sie haben keinen Zugriff auf diese Tour');
            return res.redirect('/user/komoot/tours');
        }

        // Sport-Typen holen
        const sportTypes = await sportTypeRepository.getAllSportTypes();

        // Seite rendern
        res.render('users/komoot_tour_details', {
            pageTitle: `Komoot-Tour: ${tour.name}`,
            tour,
            sportTypes
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to show Komoot tour: ${error}`);
        next(error);
    }
};

/**
 * Zeigt das Formular zur Konvertierung einer Komoot-Tour in eine Aktivität
 */
export const showConvertKomootTourForm = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const fnLogPrefix = `[KomootCtrl ShowConvertForm User:${req.user?.id} TourID:${req.params.id}]`;

    try {
        const tourId = parseInt(req.params.id, 10);

        if (isNaN(tourId)) {
            req.flash('error', 'Ungültige Tour-ID');
            return res.redirect('/user/komoot/tours');
        }

        // Tour holen
        const tour = await komootService.getTour(tourId);

        if (!tour) {
            req.flash('error', 'Tour nicht gefunden');
            return res.redirect('/user/komoot/tours');
        }

        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user!.id) {
            req.flash('error', 'Sie haben keinen Zugriff auf diese Tour');
            return res.redirect('/user/komoot/tours');
        }

        // Formular zur Konvertierung anzeigen
        res.render('users/komoot_tour_convert', {
            pageTitle: 'Komoot-Tour konvertieren',
            tour
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to show convert form: ${error}`);
        req.flash('error', 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        res.redirect('/user/komoot/tours');
    }
};

/**
 * Konvertiert eine Komoot-Tour in eine Aktivität oder geplante Route
 */
export const convertKomootTourToActivity = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const fnLogPrefix = `[KomootCtrl ConvertTour User:${req.user?.id} TourID:${req.params.id}]`;

    try {
        const tourId = parseInt(req.params.id, 10);

        if (isNaN(tourId)) {
            req.flash('error', 'Ungültige Tour-ID');
            return res.redirect('/user/komoot/tours');
        }

        // Tour holen
        const tour = await komootService.getTour(tourId);

        if (!tour) {
            req.flash('error', 'Tour nicht gefunden');
            return res.redirect('/user/komoot/tours');
        }

        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user!.id) {
            req.flash('error', 'Sie haben keinen Zugriff auf diese Tour');
            return res.redirect('/user/komoot/tours');
        }

        // Formulardaten holen
        const {
            activity_name,
            sport_type,
            activity_type,
            private_note,
            extract_elevation
        } = req.body;

        // Konvertierung durchführen
        const result = await komootService.convertTourToActivity({
            tour,
            userId: req.user!.id,
            activityName: activity_name,
            sportType: sport_type,
            activityType: activity_type,
            privateNote: private_note,
            extractElevation: extract_elevation === '1'
        });

        if (result.success) {
            req.flash('success', result.message);
            res.redirect('/user/komoot/tours');
        } else {
            req.flash('error', result.message || 'Ein Fehler ist bei der Konvertierung aufgetreten.');
            res.redirect(`/user/komoot/tours/${tourId}`);
        }
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to convert Komoot tour to activity: ${error}`);
        req.flash('error', 'Ein Fehler ist bei der Konvertierung aufgetreten. Bitte versuchen Sie es später erneut.');
        res.redirect(`/user/komoot/tours/${req.params.id}`);
    }
};

/**
 * Zeigt die Synchronisierungsseite an
 */
export const showKomootSync = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const fnLogPrefix = `[KomootCtrl ShowSync User:${req.user?.id}]`;

    try {
        // Anmeldeinformationen holen
        const credentials = await komootService.getCredentials(req.user!.id);

        if (!credentials) {
            req.flash('error', 'Keine Komoot-Anmeldeinformationen gefunden. Bitte konfigurieren Sie Ihre Komoot-Einstellungen.');
            return res.redirect('/user/komoot/settings');
        }

        // Seite rendern
        res.render('users/komoot_sync', {
            pageTitle: 'Komoot-Touren synchronisieren'
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to show Komoot sync page: ${error}`);
        next(error);
    }
};

/**
 * Zeigt die Vorschau einer Komoot-Tour an
 */
export const showKomootTourPreview = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const fnLogPrefix = `[KomootCtrl ShowTourPreview User:${req.user?.id} TourID:${req.params.id}]`;

    try {
        const tourId = parseInt(req.params.id, 10);

        if (isNaN(tourId)) {
            req.flash('error', 'Ungültige Tour-ID');
            return res.redirect('/user/komoot/tours');
        }

        // Tour holen
        const tour = await komootService.getTour(tourId);

        if (!tour) {
            req.flash('error', 'Tour nicht gefunden');
            return res.redirect('/user/komoot/tours');
        }

        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user!.id) {
            req.flash('error', 'Sie haben keinen Zugriff auf diese Tour');
            return res.redirect('/user/komoot/tours');
        }

        // Sport-Typen holen
        const sportTypes = await sportTypeRepository.getAllSportTypes();

        // Seite rendern
        res.render('users/komoot_tour_preview', {
            pageTitle: `Vorschau: ${tour.name}`,
            tour,
            sportTypes
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to show Komoot tour preview: ${error}`);
        next(error);
    }
};

/**
 * Liefert die GPX-Daten einer Komoot-Tour
 */
export const getKomootTourGpx = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
    const fnLogPrefix = `[KomootCtrl GetTourGpx User:${req.user?.id} TourID:${req.params.id}]`;

    try {
        const tourId = parseInt(req.params.id, 10);

        if (isNaN(tourId)) {
            return res.status(400).json({ success: false, message: 'Ungültige Tour-ID' });
        }

        // Tour holen
        const tour = await komootService.getTour(tourId);

        if (!tour) {
            return res.status(404).json({ success: false, message: 'Tour nicht gefunden' });
        }

        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user!.id) {
            return res.status(403).json({ success: false, message: 'Sie haben keinen Zugriff auf diese Tour' });
        }

        // GPX-Daten holen
        const gpxData = await komootService.getGpxData(tour);

        if (!gpxData) {
            return res.status(404).json({ success: false, message: 'GPX-Datei nicht gefunden' });
        }

        res.json({ success: true, gpxData });
    } catch (error) {
        log.error(`${fnLogPrefix} Failed to get Komoot tour GPX: ${error}`);
        res.status(500).json({ success: false, message: 'Ein Fehler ist aufgetreten' });
    }
};

/**
 * Extrahiert POIs aus einer Komoot-Tour
 */
export const getKomootTourPois = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
    const fnLogPrefix = `[KomootCtrl GetTourPois User:${req.user?.id} TourID:${req.params.id}]`;

    try {
        const tourId = parseInt(req.params.id, 10);

        if (isNaN(tourId)) {
            return res.status(400).json({ success: false, message: 'Ungültige Tour-ID' });
        }

        // Tour holen
        const tour = await komootService.getTour(tourId);

        if (!tour) {
            return res.status(404).json({ success: false, message: 'Tour nicht gefunden' });
        }

        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user!.id) {
            return res.status(403).json({ success: false, message: 'Sie haben keinen Zugriff auf diese Tour' });
        }

        // POIs aus der Tour extrahieren
        const pois = await komootService.extractPoisFromTour(tour);

        if (!pois) {
            return res.status(404).json({ success: false, message: 'Keine POIs gefunden oder GPX-Datei nicht verfügbar' });
        }

        res.json({
            success: true,
            data: pois
        });
    } catch (error) {
        log.error(`${fnLogPrefix} Error extracting POIs: ${error}`);
        res.status(500).json({ success: false, message: 'Interner Serverfehler' });
    }
};

/**
 * Importiert ausgewählte POIs aus einer Komoot-Tour
 */
export const importKomootTourPois = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
    const fnLogPrefix = `[KomootCtrl ImportTourPois User:${req.user?.id} TourID:${req.params.id}]`;

    try {
        const tourId = parseInt(req.params.id, 10);

        if (isNaN(tourId)) {
            req.flash('error', 'Ungültige Tour-ID');
            return res.redirect('/user/komoot/tours');
        }

        // Tour holen
        const tour = await komootService.getTour(tourId);

        if (!tour) {
            req.flash('error', 'Tour nicht gefunden');
            return res.redirect('/user/komoot/tours');
        }

        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user!.id) {
            req.flash('error', 'Sie haben keinen Zugriff auf diese Tour');
            return res.redirect('/user/komoot/tours');
        }

        // Ausgewählte POIs aus dem Request holen
        const { selected_pois } = req.body;

        if (!selected_pois || !Array.isArray(selected_pois)) {
            req.flash('error', 'Keine POIs ausgewählt');
            return res.redirect(`/user/komoot/tours/${tourId}`);
        }

        // POI-Indizes in Zahlen umwandeln
        const selectedPoiIndices = selected_pois.map(index => parseInt(index, 10)).filter(index => !isNaN(index));

        if (selectedPoiIndices.length === 0) {
            req.flash('error', 'Keine gültigen POIs ausgewählt');
            return res.redirect(`/user/komoot/tours/${tourId}`);
        }

        // POIs importieren
        const result = await komootService.importPoisFromTour(tour, req.user!.id, selectedPoiIndices);

        if (result.success) {
            req.flash('success', result.message);
        } else {
            req.flash('error', result.message);
        }

        res.redirect(`/user/komoot/tours/${tourId}`);
    } catch (error) {
        log.error(`${fnLogPrefix} Error importing POIs: ${error}`);
        req.flash('error', 'Ein Fehler ist beim Import der POIs aufgetreten.');
        res.redirect(`/user/komoot/tours/${req.params.id}`);
    }
};
