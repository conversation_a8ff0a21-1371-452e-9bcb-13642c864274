/**
 * Komoot-Benutzer-Schnittstelle
 * Speichert die Informationen eines Komoot-Benutzers
 */
export interface KomootUser {
    id: number;
    user_id: number;
    komoot_email: string;
    komoot_password: string;
    komoot_user_id: string;
    last_sync?: Date;
    created_at: Date;
    updated_at: Date;
}

/**
 * Komoot-Tour-Schnittstelle
 * Speichert die Informationen einer Komoot-Tour
 */
export interface KomootTour {
    id: number;
    user_id: number;
    komoot_id: string;
    name: string;
    sport_type: string;
    status: string;
    date: Date;
    distance: number;
    duration: number;
    elevation_up: number;
    elevation_down: number;
    difficulty?: string;
    start_point?: string;
    end_point?: string;
    start_lat?: number;
    start_lng?: number;
    end_lat?: number;
    end_lng?: number;
    gpx_path?: string;
    komoot_url: string;
    is_imported?: number; // Flag, ob die Tour bereits importiert wurde (0 oder 1)
    synced_at: Date;
    created_at: Date;
    updated_at: Date;
}

/**
 * Komoot-Highlight-Schnittstelle
 * Speichert die Informationen eines Komoot-Highlights
 */
export interface KomootHighlight {
    id: number;
    tour_id: number;
    komoot_id: string;
    name: string;
    description?: string;
    lat?: number;
    lng?: number;
    image_url?: string;
    komoot_url?: string;
    created_at: Date;
    updated_at: Date;
}

/**
 * Komoot-Synchronisierungsprotokoll-Schnittstelle
 * Speichert die Informationen eines Komoot-Synchronisierungsprotokolls
 */
export interface KomootSyncLog {
    id: number;
    user_id: number;
    status: 'success' | 'error';
    message?: string;
    tours_found: number;
    tours_added: number;
    tours_updated: number;
    tours_failed: number;
    created_at: Date;
}

/**
 * Komoot-Anmeldeinformationen
 * Für die Anmeldung bei der Komoot-API
 */
export interface KomootCredentials {
    email: string;
    password: string;
}

/**
 * Komoot-Synchronisierungsoptionen
 * Optionen für die Synchronisierung von Komoot-Touren
 */
export interface KomootSyncOptions {
    userId: number;
    sportTypes?: string[];
    status?: string;
    fromDate?: Date;
    toDate?: Date;
    limit?: number;
}

/**
 * Komoot-Synchronisierungsergebnis
 * Ergebnis der Synchronisierung von Komoot-Touren
 */
export interface KomootSyncResult {
    totalTours: number;
    newTours: number;
    updatedTours: number;
    failedTours: number;
    skippedTours: number;
    tours: KomootTour[];
}

/**
 * Komoot-Tour-Übersicht
 * Übersicht einer Komoot-Tour für die Anzeige in Listen
 */
export interface KomootTourSummary {
    id: number;
    komoot_id: string;
    name: string;
    sport_type: string;
    status: string;
    date: Date;
    distance: number;
    duration: number;
    elevation_up: number;
    elevation_down: number;
    gpx_path?: string;
    komoot_url: string;
    is_imported?: number; // Flag, ob die Tour bereits importiert wurde (0 oder 1)
}

/**
 * Komoot-POI-Schnittstelle
 * Speichert die Informationen eines POI aus einer Komoot-GPX-Datei
 */
export interface KomootPoi {
    name: string;
    description: string;
    latitude: number;
    longitude: number;
    type: string;
    source: string;
    komootUrl: string;
    imageUrl: string;
}

/**
 * Komoot-Konfiguration
 * Konfiguration für die Komoot-Integration
 */
export interface KomootConfig {
    enabled: boolean;
    pythonPath: string;
    scriptPath: string;
    gpxUploadDir: string;
}
