"use strict";
/**
 * Komoot Routes
 *
 * Diese Routen verwalten die Komoot-Integration.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const komootController = __importStar(require("../controllers/komootController"));
//import { requireLogin } from '../middleware/requireLogin';
const router = express_1.default.Router();
// Alle Routen erfordern eine Anmeldung
//router.use(requireLogin);
// Komoot-Einstellungen
router.get('/settings', komootController.showKomootSettings);
router.post('/settings/save', komootController.saveKomootCredentials);
router.post('/settings/delete', komootController.deleteKomootCredentials);
// Komoot-Touren
router.get('/tours', komootController.showKomootTours);
router.get('/sync', komootController.showKomootSync);
router.post('/sync', komootController.syncKomootTours);
router.get('/tours/:id', komootController.showKomootTour);
router.get('/tours/:id/gpx', komootController.getKomootTourGpx);
router.get('/tours/:id/pois', komootController.getKomootTourPois);
router.post('/tours/:id/import', komootController.convertKomootTourToActivity);
router.post('/tours/:id/import-pois', komootController.importKomootTourPois);
exports.default = router;
//# sourceMappingURL=komootRoutes.js.map