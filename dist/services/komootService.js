"use strict";
/**
 * Komoot Service
 *
 * Dieser Service verwaltet die Komoot-Integration und synchronisiert Touren.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const child_process_1 = require("child_process");
const logger_1 = __importDefault(require("../utils/logger"));
const config_1 = __importDefault(require("../config/config"));
const connection_1 = __importDefault(require("../db/connection"));
const komootGpxService_1 = __importDefault(require("./komootGpxService"));
const geoGpxUtils_1 = require("../utils/geoGpxUtils");
const gpxProcessingService = __importStar(require("./gpxProcessingService"));
const poiRepository = __importStar(require("../repositories/poiRepository"));
const log = logger_1.default.getLogger(__filename);
/**
 * Komoot Service
 */
class KomootService {
    constructor() {
        // Pfad zum GPX-Upload-Verzeichnis
        this.gpxUploadDir = config_1.default.komoot.gpxUploadDir;
        // Sicherstellen, dass das Verzeichnis existiert
        if (!fs_1.default.existsSync(this.gpxUploadDir)) {
            fs_1.default.mkdirSync(this.gpxUploadDir, { recursive: true });
        }
    }
    /**
     * Speichert die Komoot-Anmeldeinformationen eines Benutzers
     * @param userId ID des Benutzers
     * @param credentials Komoot-Anmeldeinformationen
     * @returns true, wenn erfolgreich
     */
    async saveCredentials(userId, credentials) {
        const fnLogPrefix = `[KomootService SaveCreds User:${userId}]`;
        try {
            // Überprüfen, ob die Anmeldeinformationen gültig sind
            const loginResult = await komootGpxService_1.default.login(credentials.email, credentials.password);
            if (!loginResult.success) {
                log.error(`${fnLogPrefix} Login failed: ${loginResult.error}`);
                return false;
            }
            // Anmeldeinformationen in der Datenbank speichern
            const credentialsSql = `
                INSERT INTO komoot_credentials (user_id, komoot_email, komoot_password, komoot_user_id)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    komoot_email = VALUES(komoot_email),
                    komoot_password = VALUES(komoot_password),
                    komoot_user_id = VALUES(komoot_user_id)
            `;
            await connection_1.default.execute(credentialsSql, [
                userId,
                credentials.email,
                credentials.password,
                userId.toString() // Wir verwenden die interne User-ID als Komoot-User-ID
            ]);
            // Komoot-Verbindungsstatus in der User-Tabelle aktualisieren
            const userSql = `
                UPDATE users
                SET komoot_connected = 1
                WHERE id = ?
            `;
            await connection_1.default.execute(userSql, [userId]);
            log.info(`${fnLogPrefix} Credentials saved successfully`);
            return true;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to save credentials: ${error}`);
            return false;
        }
    }
    /**
     * Holt die Komoot-Anmeldeinformationen eines Benutzers
     * @param userId ID des Benutzers
     * @returns Komoot-Anmeldeinformationen oder null
     */
    async getCredentials(userId) {
        const fnLogPrefix = `[KomootService GetCreds User:${userId}]`;
        try {
            const sql = `
                SELECT komoot_email, komoot_password, komoot_user_id, last_sync
                FROM komoot_credentials
                WHERE user_id = ?
            `;
            const [rows] = await connection_1.default.query(sql, [userId]);
            if (!Array.isArray(rows) || rows.length === 0) {
                return null;
            }
            const row = rows[0];
            return {
                email: row.komoot_email,
                password: row.komoot_password
            };
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to get credentials: ${error}`);
            return null;
        }
    }
    /**
     * Löscht die Komoot-Anmeldeinformationen eines Benutzers
     * @param userId ID des Benutzers
     * @returns true, wenn erfolgreich
     */
    async deleteCredentials(userId) {
        const fnLogPrefix = `[KomootService DeleteCreds User:${userId}]`;
        try {
            // Anmeldeinformationen löschen
            const credentialsSql = `
                DELETE FROM komoot_credentials
                WHERE user_id = ?
            `;
            await connection_1.default.execute(credentialsSql, [userId]);
            // Komoot-Verbindungsstatus in der User-Tabelle aktualisieren
            const userSql = `
                UPDATE users
                SET komoot_connected = 0
                WHERE id = ?
            `;
            await connection_1.default.execute(userSql, [userId]);
            log.info(`${fnLogPrefix} Credentials deleted successfully`);
            return true;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to delete credentials: ${error}`);
            return false;
        }
    }
    /**
     * Synchronisiert die Komoot-Touren eines Benutzers
     * @param options Synchronisierungsoptionen
     * @returns Synchronisierungsergebnis
     */
    async syncTours(options) {
        const fnLogPrefix = `[KomootService SyncTours User:${options.userId}]`;
        const result = {
            totalTours: 0,
            newTours: 0,
            updatedTours: 0,
            failedTours: 0,
            skippedTours: 0,
            tours: []
        };
        try {
            // Anmeldeinformationen holen
            const credentials = await this.getCredentials(options.userId);
            if (!credentials) {
                log.error(`${fnLogPrefix} No credentials found`);
                throw new Error('No Komoot credentials found');
            }
            // Touren von Komoot holen
            const toursResult = await komootGpxService_1.default.getTours(credentials.email, credentials.password, {
                type: options.status || 'all',
                sport: options.sportTypes && options.sportTypes.length > 0 ? options.sportTypes[0] : undefined,
                limit: options.limit
            });
            if (!toursResult.success || !toursResult.tours) {
                log.error(`${fnLogPrefix} Failed to get tours: ${toursResult.error}`);
                throw new Error(`Failed to get Komoot tours: ${toursResult.error}`);
            }
            result.totalTours = toursResult.tours.length;
            log.info(`${fnLogPrefix} Found ${result.totalTours} tours`);
            // Wir verwenden die Touren direkt, da die Filterung bereits im komootGpxService erfolgt ist
            const filteredTours = toursResult.tours;
            log.info(`${fnLogPrefix} Processing ${filteredTours.length} tours`);
            // Touren synchronisieren
            for (const tour of filteredTours) {
                try {
                    // Überprüfen, ob die Tour bereits existiert
                    const existingTour = await this.getTourByKomootId(options.userId, tour.id.toString());
                    // Wenn die Tour bereits existiert und wir sie nicht aktualisieren wollen, überspringen
                    if (existingTour) {
                        log.info(`${fnLogPrefix} Tour ${tour.id} already exists, skipping`);
                        result.skippedTours++;
                        continue;
                    }
                    // GPX-Datei wird erst bei Bedarf generiert
                    log.info(`${fnLogPrefix} Skipping GPX download for tour ${tour.id} - will be downloaded on demand`);
                    // Tour in der Datenbank speichern
                    const tourData = {
                        user_id: options.userId,
                        komoot_id: tour.id.toString(),
                        name: tour.name,
                        sport_type: tour.sport,
                        status: tour.status.includes('planned') ? 'planned' : 'recorded',
                        date: new Date(),
                        distance: tour.distance * 1000, // Umrechnung von km in m
                        duration: 0, // Nicht verfügbar
                        elevation_up: 0, // Nicht verfügbar
                        elevation_down: 0, // Nicht verfügbar
                        // GPX-Pfad auf null setzen, damit er in der Datenbank NULL ist
                        gpx_path: null,
                        komoot_url: `https://www.komoot.de/tour/${tour.id}`
                    };
                    // Neue Tour erstellen
                    const newTour = await this.createTour(tourData);
                    result.newTours++;
                    result.tours.push(newTour);
                }
                catch (error) {
                    log.error(`${fnLogPrefix} Failed to sync tour ${tour.id}: ${error}`);
                    result.failedTours++;
                }
            }
            // Letzten Synchronisierungszeitpunkt aktualisieren
            await this.updateLastSync(options.userId);
            // Synchronisierungsprotokoll erstellen
            await this.logSyncResult(options.userId, {
                status: 'success',
                toursFound: result.totalTours,
                toursAdded: result.newTours,
                toursUpdated: result.updatedTours,
                toursFailed: result.failedTours
            });
            return result;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to sync tours: ${error}`);
            // Synchronisierungsprotokoll erstellen
            await this.logSyncResult(options.userId, {
                status: 'error',
                message: error.message,
                toursFound: result.totalTours,
                toursAdded: result.newTours,
                toursUpdated: result.updatedTours,
                toursFailed: result.failedTours
            });
            throw error;
        }
    }
    /**
     * Holt eine Tour anhand ihrer Komoot-ID
     * @param userId ID des Benutzers
     * @param komootId Komoot-ID der Tour
     * @returns Tour oder null
     */
    async getTourByKomootId(userId, komootId) {
        const fnLogPrefix = `[KomootService GetTourByKomootId User:${userId} KomootID:${komootId}]`;
        try {
            const sql = `
                SELECT *
                FROM komoot_tours
                WHERE user_id = ? AND komoot_id = ?
            `;
            const [rows] = await connection_1.default.query(sql, [userId, komootId]);
            if (!Array.isArray(rows) || rows.length === 0) {
                return null;
            }
            return rows[0];
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to get tour: ${error}`);
            return null;
        }
    }
    /**
     * Erstellt eine neue Tour
     * @param tourData Tour-Daten
     * @returns Erstellte Tour
     */
    async createTour(tourData) {
        const fnLogPrefix = `[KomootService CreateTour User:${tourData.user_id} KomootID:${tourData.komoot_id}]`;
        try {
            // Debug-Ausgabe der Tourdaten
            log.debug(`${fnLogPrefix} Tour data: ${JSON.stringify(tourData)}`);
            // Sicherstellen, dass keine undefined-Werte übergeben werden
            const params = [
                tourData.user_id,
                tourData.komoot_id,
                tourData.name,
                tourData.sport_type,
                tourData.status,
                tourData.date,
                tourData.distance,
                tourData.duration,
                tourData.elevation_up,
                tourData.elevation_down,
                tourData.difficulty === undefined ? null : tourData.difficulty,
                tourData.start_point === undefined ? null : tourData.start_point,
                tourData.end_point === undefined ? null : tourData.end_point,
                tourData.start_lat === undefined ? null : tourData.start_lat,
                tourData.start_lng === undefined ? null : tourData.start_lng,
                tourData.end_lat === undefined ? null : tourData.end_lat,
                tourData.end_lng === undefined ? null : tourData.end_lng,
                tourData.gpx_path,
                tourData.komoot_url
            ];
            // Debug-Ausgabe der Parameter
            log.debug(`${fnLogPrefix} SQL parameters: ${JSON.stringify(params)}`);
            const sql = `
                INSERT INTO komoot_tours (
                    user_id, komoot_id, name, sport_type, status, date,
                    distance, duration, elevation_up, elevation_down, difficulty,
                    start_point, end_point, start_lat, start_lng, end_lat, end_lng,
                    gpx_path, komoot_url
                ) VALUES (
                    ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?,
                    ?, ?
                )
            `;
            const [result] = await connection_1.default.execute(sql, params);
            log.info(`${fnLogPrefix} Tour created with ID ${result.insertId}`);
            return {
                ...tourData,
                id: result.insertId,
                synced_at: new Date(),
                created_at: new Date(),
                updated_at: new Date()
            };
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to create tour: ${error}`);
            throw error;
        }
    }
    /**
     * Aktualisiert eine Tour
     * @param tourId ID der Tour
     * @param tourData Tour-Daten
     * @returns true, wenn erfolgreich
     */
    async updateTour(tourId, tourData) {
        const fnLogPrefix = `[KomootService UpdateTour ID:${tourId} KomootID:${tourData.komoot_id}]`;
        try {
            const sql = `
                UPDATE komoot_tours
                SET
                    name = ?,
                    sport_type = ?,
                    status = ?,
                    date = ?,
                    distance = ?,
                    duration = ?,
                    elevation_up = ?,
                    elevation_down = ?,
                    difficulty = ?,
                    start_point = ?,
                    end_point = ?,
                    start_lat = ?,
                    start_lng = ?,
                    end_lat = ?,
                    end_lng = ?,
                    gpx_path = ?,
                    komoot_url = ?,
                    synced_at = NOW()
                WHERE id = ?
            `;
            await connection_1.default.execute(sql, [
                tourData.name,
                tourData.sport_type,
                tourData.status,
                tourData.date,
                tourData.distance,
                tourData.duration,
                tourData.elevation_up,
                tourData.elevation_down,
                tourData.difficulty,
                tourData.start_point,
                tourData.end_point,
                tourData.start_lat,
                tourData.start_lng,
                tourData.end_lat,
                tourData.end_lng,
                tourData.gpx_path,
                tourData.komoot_url,
                tourId
            ]);
            log.info(`${fnLogPrefix} Tour updated successfully`);
            return true;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to update tour: ${error}`);
            throw error;
        }
    }
    /**
     * Aktualisiert den letzten Synchronisierungszeitpunkt
     * @param userId ID des Benutzers
     * @returns true, wenn erfolgreich
     */
    async updateLastSync(userId) {
        const fnLogPrefix = `[KomootService UpdateLastSync User:${userId}]`;
        try {
            const sql = `
                UPDATE komoot_credentials
                SET last_sync = NOW()
                WHERE user_id = ?
            `;
            await connection_1.default.execute(sql, [userId]);
            log.info(`${fnLogPrefix} Last sync updated successfully`);
            return true;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to update last sync: ${error}`);
            return false;
        }
    }
    /**
     * Speichert das Ergebnis einer Synchronisierung in der Datenbank
     * @param userId ID des Benutzers
     * @param logEntry Synchronisierungsprotokoll-Eintrag
     * @returns true, wenn erfolgreich
     */
    async logSyncResult(userId, logEntry) {
        const fnLogPrefix = `[KomootService LogSyncResult User:${userId}]`;
        try {
            const sql = `
                INSERT INTO komoot_sync_log (
                    user_id, status, message, tours_found, tours_added, tours_updated, tours_failed
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?
                )
            `;
            await connection_1.default.execute(sql, [
                userId,
                logEntry.status,
                logEntry.message || null,
                logEntry.toursFound,
                logEntry.toursAdded,
                logEntry.toursUpdated,
                logEntry.toursFailed
            ]);
            log.info(`${fnLogPrefix} Sync result logged successfully`);
            return true;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to log sync result: ${error}`);
            return false;
        }
    }
    /**
     * Holt alle Touren eines Benutzers
     * @param userId ID des Benutzers
     * @returns Liste der Touren
     */
    async getTours(userId) {
        const fnLogPrefix = `[KomootService GetTours User:${userId}]`;
        try {
            const sql = `
                SELECT *
                FROM komoot_tours
                WHERE user_id = ?
                ORDER BY date DESC
            `;
            const [rows] = await connection_1.default.query(sql, [userId]);
            if (!Array.isArray(rows)) {
                return [];
            }
            return rows;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to get tours: ${error}`);
            return [];
        }
    }
    /**
     * Konvertiert eine Komoot-Tour in eine Aktivität oder geplante Route
     * @param options Konvertierungsoptionen
     * @returns Ergebnis der Konvertierung
     */
    async convertTourToActivity(options) {
        const fnLogPrefix = `[KomootService ConvertTour User:${options.userId} TourID:${options.tour.id}]`;
        try {
            // GPX-Datei prüfen
            if (!options.tour.gpx_path) {
                log.error(`${fnLogPrefix} No GPX file specified for tour ${options.tour.id}`);
                return { success: false, message: 'Keine GPX-Datei für diese Tour vorhanden' };
            }
            const gpxPath = path_1.default.join(this.gpxUploadDir, options.tour.gpx_path);
            if (!fs_1.default.existsSync(gpxPath)) {
                log.error(`${fnLogPrefix} GPX file not found: ${gpxPath}`);
                return { success: false, message: 'GPX-Datei nicht gefunden' };
            }
            // Höhendaten aus GPX-Datei extrahieren, falls gewünscht
            let elevationUp = options.tour.elevation_up;
            let elevationDown = options.tour.elevation_down;
            let distance = options.tour.distance;
            let duration = options.tour.duration;
            if (options.extractElevation) {
                const gpxData = (0, geoGpxUtils_1.parseGpx)(gpxPath);
                if (gpxData) {
                    if (gpxData.elevationGain && gpxData.elevationGain > 0) {
                        elevationUp = Math.round(gpxData.elevationGain);
                    }
                    if (gpxData.elevationLoss && gpxData.elevationLoss > 0) {
                        elevationDown = Math.round(gpxData.elevationLoss);
                    }
                    if (gpxData.distance && gpxData.distance > 0) {
                        distance = Math.round(gpxData.distance);
                    }
                    if (gpxData.duration && gpxData.duration > 0) {
                        duration = Math.round(gpxData.duration);
                    }
                }
            }
            // Prüfen, ob bereits eine Aktivität mit diesem Namen existiert
            const existingActivitySql = `
                SELECT id FROM activities
                WHERE user_id = ? AND activity_name = ?
            `;
            const [existingActivities] = await connection_1.default.query(existingActivitySql, [
                options.userId,
                options.activityName
            ]);
            if (Array.isArray(existingActivities) && existingActivities.length > 0) {
                log.warn(`${fnLogPrefix} Activity with name ${options.activityName} already exists`);
                return {
                    success: false,
                    message: 'Eine Aktivität mit diesem Namen existiert bereits',
                    activityId: existingActivities[0].id
                };
            }
            // Prüfen, ob bereits eine geplante Route mit dieser GPX-Datei existiert
            const existingRouteSql = `
                SELECT id FROM planned_routes
                WHERE user_id = ? AND gpx_filename = ?
            `;
            const [existingRoutes] = await connection_1.default.query(existingRouteSql, [
                options.userId,
                options.tour.gpx_path
            ]);
            if (Array.isArray(existingRoutes) && existingRoutes.length > 0) {
                log.warn(`${fnLogPrefix} Planned route with Komoot ID ${options.tour.komoot_id} already exists`);
                return {
                    success: false,
                    message: 'Eine geplante Route mit dieser Komoot-ID existiert bereits',
                    activityId: existingRoutes[0].id
                };
            }
            // Je nach Aktivitätstyp in die entsprechende Tabelle einfügen
            if (options.activityType === 'activity') {
                // In activities-Tabelle einfügen
                const activitySql = `
                    INSERT INTO activities (
                        user_id, komoot_id, activity_name, start_date, start_date_local,
                        sport_type, distance, total_elevation_gain, moving_time, elapsed_time,
                        start_lat, start_lng, end_lat, end_lng, location_country,
                        private_note, average_speed, max_speed, photo_count, is_trainer
                    ) VALUES (
                        ?, ?, ?, ?, ?,
                        ?, ?, ?, ?, ?,
                        ?, ?, ?, ?, ?,
                        ?, ?, ?, ?, ?
                    )
                `;
                const now = new Date();
                const activityParams = [
                    options.userId,
                    options.tour.komoot_id,
                    options.activityName,
                    now,
                    now,
                    options.sportType,
                    distance,
                    elevationUp,
                    duration,
                    duration,
                    options.tour.start_lat || null,
                    options.tour.start_lng || null,
                    options.tour.end_lat || null,
                    options.tour.end_lng || null,
                    null, // location_country
                    options.privateNote || null, // private_note
                    distance > 0 && duration > 0 ? (distance / duration) * 3.6 : 0, // average_speed (km/h)
                    0, // max_speed
                    0, // photo_count
                    0 // is_trainer
                ];
                const [activityResult] = await connection_1.default.execute(activitySql, activityParams);
                const activityId = activityResult.insertId;
                // Setze das is_imported Flag in der komoot_tours Tabelle
                const updateTourSql = `
                    UPDATE komoot_tours
                    SET is_imported = 1
                    WHERE id = ?
                `;
                await connection_1.default.execute(updateTourSql, [options.tour.id]);
                log.info(`${fnLogPrefix} Activity created with ID ${activityId} and tour marked as imported`);
                return {
                    success: true,
                    message: 'Aktivität erfolgreich erstellt',
                    activityId
                };
            }
            else {
                // In planned_routes-Tabelle einfügen
                const routeSql = `
                    INSERT INTO planned_routes (
                        user_id, name, description, gpx_filename, distance_m,
                        elevation_gain_m, komoot_id, source, source_url, sport_type
                    ) VALUES (
                        ?, ?, ?, ?, ?,
                        ?, ?, ?, ?, ?
                    )
                `;
                // Temporärer Dateiname für die Datenbank
                const tempGpxFilename = options.tour.gpx_path;
                const routeParams = [
                    options.userId,
                    options.activityName,
                    options.privateNote || null,
                    tempGpxFilename, // Temporärer Dateiname, wird später aktualisiert
                    distance / 1000, // Umrechnung in km
                    elevationUp,
                    options.tour.komoot_id,
                    'komoot',
                    options.tour.komoot_url,
                    options.sportType
                ];
                const [routeResult] = await connection_1.default.execute(routeSql, routeParams);
                const routeId = routeResult.insertId;
                // Verschiebe die GPX-Datei in den planned-Ordner mit der ID als Dateinamen
                const sourceGpxPath = path_1.default.join(this.gpxUploadDir, options.tour.gpx_path);
                const plannedGpxDir = path_1.default.join(config_1.default.paths.gpxBaseDir, config_1.default.paths.plannedGpxSubDir);
                const finalGpxFilename = `${routeId}.gpx`;
                const targetGpxPath = path_1.default.join(plannedGpxDir, finalGpxFilename);
                // Stelle sicher, dass das Zielverzeichnis existiert
                if (!fs_1.default.existsSync(plannedGpxDir)) {
                    fs_1.default.mkdirSync(plannedGpxDir, { recursive: true });
                }
                try {
                    // Kopiere die Datei in den planned-Ordner
                    fs_1.default.copyFileSync(sourceGpxPath, targetGpxPath);
                    log.info(`${fnLogPrefix} GPX file copied to: ${targetGpxPath}`);
                    // Aktualisiere den Dateinamen in der Datenbank
                    const updateSql = `UPDATE planned_routes SET gpx_filename = ? WHERE id = ?`;
                    await connection_1.default.execute(updateSql, [finalGpxFilename, routeId]);
                    log.info(`${fnLogPrefix} Updated gpx_filename in database to: ${finalGpxFilename}`);
                }
                catch (error) {
                    log.error(`${fnLogPrefix} Failed to copy GPX file: ${error}`);
                    // Wir werfen hier keinen Fehler, da die Route bereits erstellt wurde
                }
                // Setze das is_imported Flag in der komoot_tours Tabelle
                const updateTourSql = `
                    UPDATE komoot_tours
                    SET is_imported = 1
                    WHERE id = ?
                `;
                await connection_1.default.execute(updateTourSql, [options.tour.id]);
                log.info(`${fnLogPrefix} Planned route created with ID ${routeId} and tour marked as imported`);
                return {
                    success: true,
                    message: 'Geplante Route erfolgreich erstellt',
                    activityId: routeId
                };
            }
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to convert tour to activity: ${error}`);
            return {
                success: false,
                message: `Fehler bei der Konvertierung: ${error.message}`
            };
        }
    }
    /**
     * Holt eine Tour anhand ihrer ID
     * @param tourId ID der Tour
     * @returns Tour oder null
     */
    async getTour(tourId) {
        const fnLogPrefix = `[KomootService GetTour ID:${tourId}]`;
        try {
            const sql = `
                SELECT *
                FROM komoot_tours
                WHERE id = ?
            `;
            const [rows] = await connection_1.default.query(sql, [tourId]);
            if (!Array.isArray(rows) || rows.length === 0) {
                return null;
            }
            return rows[0];
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to get tour: ${error}`);
            return null;
        }
    }
    /**
     * Prüft, welche Touren bereits in geplante Routen oder Aktivitäten konvertiert wurden
     * @param userId Benutzer-ID
     * @returns Array von Komoot-IDs, die bereits konvertiert wurden
     */
    async getConvertedTours(userId) {
        const fnLogPrefix = `[KomootService GetConvertedTours User:${userId}]`;
        try {
            // Verwende das is_imported Flag aus der komoot_tours Tabelle
            const sql = `
                SELECT komoot_id FROM komoot_tours
                WHERE user_id = ? AND is_imported = 1
            `;
            const [rows] = await connection_1.default.query(sql, [userId]);
            const komootIds = rows.map(row => row.komoot_id);
            log.info(`${fnLogPrefix} Found ${komootIds.length} imported tours based on is_imported flag`);
            return komootIds;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to get converted tours: ${error}`);
            return [];
        }
    }
    /**
     * Holt die GPX-Daten einer Tour
     * @param tour Komoot-Tour
     * @returns GPX-Daten oder null, wenn ein Fehler auftritt
     */
    async getGpxData(tour) {
        const fnLogPrefix = `[KomootService GetGpxData TourID:${tour.id}]`;
        try {
            // Temporäres Verzeichnis für GPX-Dateien
            const tempDir = path_1.default.join(config_1.default.paths.tempDir || '/tmp', 'komoot-preview');
            // Prüfe, ob die Datei bereits im Cache ist
            const cacheKey = `komoot_${tour.id}`;
            const cachedFilePath = await gpxProcessingService.getFromCache(tempDir, cacheKey);
            if (cachedFilePath) {
                log.info(`${fnLogPrefix} Using cached GPX file: ${cachedFilePath}`);
                // Verarbeite die GPX-Datei aus dem Cache
                const result = await gpxProcessingService.processLocalGpxFile(cachedFilePath, cacheKey);
                if (result.success && result.gpxData) {
                    return result.gpxData;
                }
                // Wenn die Verarbeitung fehlschlägt, versuche erneut herunterzuladen
                log.warn(`${fnLogPrefix} Failed to process cached GPX file, trying to download again`);
            }
            // Prüfen, ob die GPX-Datei bereits heruntergeladen wurde
            if (!tour.gpx_path) {
                // GPX-Datei herunterladen
                const credentials = await this.getCredentials(tour.user_id);
                if (!credentials) {
                    log.error(`${fnLogPrefix} No credentials found for user ${tour.user_id}`);
                    return null;
                }
                // Komoot-GPX-Service aufrufen
                const gpxPath = await this.downloadTourGpx(tour, credentials);
                if (!gpxPath) {
                    log.error(`${fnLogPrefix} Failed to download GPX file`);
                    return null;
                }
                // GPX-Pfad in der Datenbank aktualisieren
                await this.updateTourGpxPath(tour.id, gpxPath);
                tour.gpx_path = gpxPath;
            }
            // GPX-Datei parsen
            const gpxFilePath = path_1.default.join(this.gpxUploadDir, tour.gpx_path || '');
            if (!fs_1.default.existsSync(gpxFilePath)) {
                log.error(`${fnLogPrefix} GPX file not found: ${gpxFilePath}`);
                return null;
            }
            // Verarbeite die GPX-Datei und speichere sie im Cache
            const result = await gpxProcessingService.processLocalGpxFile(gpxFilePath, cacheKey, tempDir);
            if (!result.success || !result.gpxData) {
                log.error(`${fnLogPrefix} Failed to process GPX file: ${result.message}`);
                return null;
            }
            return result.gpxData;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to get GPX data: ${error}`);
            return null;
        }
    }
    /**
     * Lädt die GPX-Datei einer Tour herunter
     * @param tour Komoot-Tour
     * @param credentials Komoot-Anmeldeinformationen
     * @returns Pfad zur GPX-Datei oder null, wenn ein Fehler auftritt
     */
    async downloadTourGpx(tour, credentials) {
        const fnLogPrefix = `[KomootService DownloadTourGpx TourID:${tour.id}]`;
        try {
            // Komoot-GPX-Service aufrufen mit vollständigem Pfad
            const komootGpxPath = config_1.default.komoot.komootGpxPath;
            // Verwende -m für E-Mail, -p für Passwort, -d für die Tour-ID
            const command = `${komootGpxPath} -m "${credentials.email}" -p "${credentials.password}" -d "${tour.komoot_id}" -o "${this.gpxUploadDir}" -I`;
            log.debug(`${fnLogPrefix} Executing command: ${command}`);
            return new Promise((resolve, reject) => {
                (0, child_process_1.exec)(command, (error, stdout, stderr) => {
                    if (error) {
                        log.error(`${fnLogPrefix} Failed to download GPX file: ${error.message}`);
                        log.error(`${fnLogPrefix} stderr: ${stderr}`);
                        resolve(null);
                        return;
                    }
                    if (stderr) {
                        log.warn(`${fnLogPrefix} stderr: ${stderr}`);
                    }
                    log.debug(`${fnLogPrefix} Command output: ${stdout}`);
                    // Extrahiere den Dateinamen aus der Ausgabe
                    // Suche nach verschiedenen möglichen Ausgabeformaten
                    const match = stdout.match(/Saved GPX file to: (.+\.gpx)/) ||
                        stdout.match(/GPX file written to ['"](.+\.gpx)['"]/);
                    if (match && match[1]) {
                        const fullPath = match[1];
                        const filename = path_1.default.basename(fullPath);
                        log.info(`${fnLogPrefix} Downloaded GPX file: ${filename}`);
                        // Überprüfe, ob die Datei existiert
                        const filePath = path_1.default.join(this.gpxUploadDir, filename);
                        if (fs_1.default.existsSync(filePath)) {
                            log.info(`${fnLogPrefix} GPX file exists at: ${filePath}`);
                            // Überprüfe die Dateigröße
                            const stats = fs_1.default.statSync(filePath);
                            log.info(`${fnLogPrefix} GPX file size: ${stats.size} bytes`);
                            resolve(filename);
                        }
                        else {
                            log.error(`${fnLogPrefix} GPX file does not exist at: ${filePath}`);
                            resolve(null);
                        }
                    }
                    else {
                        log.error(`${fnLogPrefix} Could not extract filename from output: ${stdout}`);
                        resolve(null);
                    }
                });
            });
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to download GPX file: ${error}`);
            return null;
        }
    }
    /**
     * Aktualisiert den GPX-Pfad einer Tour
     * @param tourId Tour-ID
     * @param gpxPath GPX-Pfad
     * @returns true, wenn erfolgreich
     */
    async updateTourGpxPath(tourId, gpxPath) {
        const fnLogPrefix = `[KomootService UpdateTourGpxPath ID:${tourId}]`;
        try {
            const sql = `
                UPDATE komoot_tours
                SET gpx_path = ?
                WHERE id = ?
            `;
            await connection_1.default.execute(sql, [gpxPath, tourId]);
            return true;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to update tour GPX path: ${error}`);
            return false;
        }
    }
    /**
     * Extrahiert POIs aus der GPX-Datei einer Tour
     * @param tour Komoot-Tour
     * @returns Array von POI-Objekten oder null bei Fehler
     */
    async extractPoisFromTour(tour) {
        const fnLogPrefix = `[KomootService ExtractPoisFromTour TourID:${tour.id}]`;
        try {
            // Sicherstellen, dass die GPX-Datei verfügbar ist
            if (!tour.gpx_path) {
                // GPX-Datei herunterladen
                const credentials = await this.getCredentials(tour.user_id);
                if (!credentials) {
                    log.error(`${fnLogPrefix} No credentials found for user ${tour.user_id}`);
                    return null;
                }
                const gpxPath = await this.downloadTourGpx(tour, credentials);
                if (!gpxPath) {
                    log.error(`${fnLogPrefix} Failed to download GPX file`);
                    return null;
                }
                // GPX-Pfad in der Datenbank aktualisieren
                await this.updateTourGpxPath(tour.id, gpxPath);
                tour.gpx_path = gpxPath;
            }
            // GPX-Datei-Pfad erstellen
            const gpxFilePath = path_1.default.join(this.gpxUploadDir, tour.gpx_path);
            if (!fs_1.default.existsSync(gpxFilePath)) {
                log.error(`${fnLogPrefix} GPX file not found: ${gpxFilePath}`);
                return null;
            }
            // POIs aus GPX-Datei extrahieren
            const pois = (0, geoGpxUtils_1.extractPoisFromGpx)(gpxFilePath);
            log.info(`${fnLogPrefix} Extracted ${pois.length} POIs from tour`);
            return pois;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to extract POIs: ${error}`);
            return null;
        }
    }
    /**
     * Importiert POIs aus einer Komoot-Tour
     * @param tour Komoot-Tour
     * @param userId Benutzer-ID
     * @param selectedPois Array der zu importierenden POI-Indizes
     * @returns Ergebnis des Imports
     */
    async importPoisFromTour(tour, userId, selectedPois) {
        const fnLogPrefix = `[KomootService ImportPoisFromTour User:${userId} TourID:${tour.id}]`;
        try {
            // POIs aus der Tour extrahieren
            const pois = await this.extractPoisFromTour(tour);
            if (!pois || pois.length === 0) {
                return {
                    success: false,
                    message: 'Keine POIs in der GPX-Datei gefunden',
                    importedCount: 0
                };
            }
            let importedCount = 0;
            // Ausgewählte POIs importieren
            for (const poiIndex of selectedPois) {
                if (poiIndex >= 0 && poiIndex < pois.length) {
                    const komootPoi = pois[poiIndex];
                    // POI-Typ basierend auf Komoot-Typ bestimmen
                    let poiType = 'viewpoint'; // Standard-Typ
                    if (komootPoi.type === 'POI') {
                        poiType = 'poi';
                    }
                    else if (komootPoi.type === 'Highlight') {
                        poiType = 'highlight';
                    }
                    // POI-Daten für die Datenbank vorbereiten
                    const poiData = {
                        user_id: userId,
                        poi_type: poiType,
                        title: komootPoi.name,
                        description: komootPoi.description || null,
                        latitude: komootPoi.latitude,
                        longitude: komootPoi.longitude,
                        source: 'komoot',
                        source_url: komootPoi.komootUrl || tour.komoot_url
                    };
                    // POI in der Datenbank erstellen
                    const poiId = await poiRepository.insertPOI(poiData);
                    if (poiId) {
                        importedCount++;
                        log.info(`${fnLogPrefix} Imported POI: ${komootPoi.name} with ID ${poiId}`);
                    }
                    else {
                        log.error(`${fnLogPrefix} Failed to import POI: ${komootPoi.name}`);
                    }
                }
            }
            return {
                success: true,
                message: `${importedCount} POI(s) erfolgreich importiert`,
                importedCount
            };
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to import POIs: ${error}`);
            return {
                success: false,
                message: `Fehler beim Import: ${error.message}`,
                importedCount: 0
            };
        }
    }
    /**
     * Setzt das is_imported Flag für eine Tour zurück, wenn die zugehörige Aktivität oder geplante Route gelöscht wurde
     * @param komootId Komoot-ID der Tour
     * @returns true, wenn erfolgreich
     */
    async resetImportedFlag(komootId) {
        const fnLogPrefix = `[KomootService ResetImportedFlag KomootID:${komootId}]`;
        try {
            const sql = `
                UPDATE komoot_tours
                SET is_imported = 0
                WHERE komoot_id = ?
            `;
            const [result] = await connection_1.default.execute(sql, [komootId]);
            if (result.affectedRows > 0) {
                log.info(`${fnLogPrefix} Successfully reset is_imported flag for tour with Komoot ID ${komootId}`);
                return true;
            }
            log.warn(`${fnLogPrefix} No tour found with Komoot ID ${komootId}`);
            return false;
        }
        catch (error) {
            log.error(`${fnLogPrefix} Failed to reset is_imported flag: ${error}`);
            return false;
        }
    }
}
exports.default = new KomootService();
//# sourceMappingURL=komootService.js.map