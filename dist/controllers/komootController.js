"use strict";
/**
 * Komoot Controller
 *
 * Dieser Controller verwaltet die Komoot-Integration.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.importKomootTourPois = exports.getKomootTourPois = exports.getKomootTourGpx = exports.showKomootTourPreview = exports.showKomootSync = exports.convertKomootTourToActivity = exports.showConvertKomootTourForm = exports.showKomootTour = exports.syncKomootTours = exports.showKomootTours = exports.deleteKomootCredentials = exports.saveKomootCredentials = exports.showKomootSettings = void 0;
const logger_1 = __importDefault(require("../utils/logger"));
const komootService_1 = __importDefault(require("../services/komootService"));
const sportTypeRepository = __importStar(require("../db/sportTypeRepository"));
const log = logger_1.default.getLogger(__filename);
/**
 * Zeigt die Komoot-Einstellungsseite an
 */
const showKomootSettings = async (req, res, next) => {
    const loggedInUserId = res.locals.currentUser.id;
    const activityDbId = parseInt(req.params.activityDbId, 10); // Dies IST die interne DB ID der Aktivität
    const fnLogPrefix = `[KomootCtrl ShowSettings User:${loggedInUserId} ActDB:${activityDbId}]]`;
    try {
        // Anmeldeinformationen holen
        const credentials = await komootService_1.default.getCredentials(loggedInUserId);
        // Seite rendern
        res.render('users/komoot_settings', {
            pageTitle: 'Komoot-Einstellungen',
            hasCredentials: !!credentials,
            komootEmail: credentials?.email || ''
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to show Komoot settings: ${error}`);
        next(error);
    }
};
exports.showKomootSettings = showKomootSettings;
/**
 * Speichert die Komoot-Anmeldeinformationen
 */
const saveKomootCredentials = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl SaveCreds User:${req.user?.id}]`;
    try {
        const { email, password } = req.body;
        // Validierung
        if (!email || !password) {
            req.flash('error', 'E-Mail-Adresse und Passwort sind erforderlich');
            return res.redirect('/user/komoot/settings');
        }
        // Anmeldeinformationen speichern
        const credentials = {
            email,
            password
        };
        const success = await komootService_1.default.saveCredentials(req.user.id, credentials);
        if (success) {
            req.flash('success', 'Komoot-Anmeldeinformationen erfolgreich gespeichert');
        }
        else {
            req.flash('error', 'Fehler beim Speichern der Komoot-Anmeldeinformationen. Bitte überprüfen Sie Ihre Eingaben.');
        }
        res.redirect('/user/komoot/settings');
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to save Komoot credentials: ${error}`);
        req.flash('error', 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        res.redirect('/user/komoot/settings');
    }
};
exports.saveKomootCredentials = saveKomootCredentials;
/**
 * Löscht die Komoot-Anmeldeinformationen
 */
const deleteKomootCredentials = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl DeleteCreds User:${req.user?.id}]`;
    try {
        const success = await komootService_1.default.deleteCredentials(req.user.id);
        if (success) {
            req.flash('success', 'Komoot-Anmeldeinformationen erfolgreich gelöscht');
        }
        else {
            req.flash('error', 'Fehler beim Löschen der Komoot-Anmeldeinformationen');
        }
        res.redirect('/user/komoot/settings');
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to delete Komoot credentials: ${error}`);
        req.flash('error', 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        res.redirect('/user/komoot/settings');
    }
};
exports.deleteKomootCredentials = deleteKomootCredentials;
/**
 * Zeigt die Komoot-Touren-Übersicht an
 */
const showKomootTours = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl ShowTours User:${req.user?.id}]`;
    try {
        // Anmeldeinformationen holen
        const credentials = await komootService_1.default.getCredentials(req.user.id);
        if (!credentials) {
            req.flash('error', 'Keine Komoot-Anmeldeinformationen gefunden. Bitte konfigurieren Sie Ihre Komoot-Einstellungen.');
            return res.redirect('/user/komoot/settings');
        }
        // Touren holen
        const tours = await komootService_1.default.getTours(req.user.id);
        // Wir brauchen die convertedTours Liste nicht mehr, da wir jetzt das is_imported Flag direkt in der Tour haben
        // Behalten wir sie aber für Abwärtskompatibilität bei
        const convertedTours = await komootService_1.default.getConvertedTours(req.user.id);
        // Flash-Nachrichten holen
        const successMessage = req.flash('success');
        const errorMessage = req.flash('error');
        // Debug-Ausgabe
        if (successMessage && successMessage.length > 0) {
            log.info(`${fnLogPrefix} Success message: ${successMessage}`);
        }
        if (errorMessage && errorMessage.length > 0) {
            log.info(`${fnLogPrefix} Error message: ${errorMessage}`);
        }
        // Seite rendern
        res.render('users/komoot_tours', {
            pageTitle: 'Komoot-Touren',
            tours,
            convertedTours,
            successMessage: successMessage.length > 0 ? successMessage[0] : null,
            errorMessage: errorMessage.length > 0 ? errorMessage[0] : null
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to show Komoot tours: ${error}`);
        next(error);
    }
};
exports.showKomootTours = showKomootTours;
/**
 * Synchronisiert die Komoot-Touren
 */
const syncKomootTours = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl SyncTours User:${req.user?.id}]`;
    try {
        // Anmeldeinformationen holen
        const credentials = await komootService_1.default.getCredentials(req.user.id);
        if (!credentials) {
            req.flash('error', 'Keine Komoot-Anmeldeinformationen gefunden. Bitte konfigurieren Sie Ihre Komoot-Einstellungen.');
            return res.redirect('/user/komoot/settings');
        }
        // Synchronisierungsoptionen
        const options = {
            userId: req.user.id
        };
        // Sportarten filtern
        if (req.body.sportTypes && Array.isArray(req.body.sportTypes)) {
            options.sportTypes = req.body.sportTypes;
        }
        // Status filtern
        if (req.body.status) {
            options.status = req.body.status;
        }
        // Datumsbereich filtern
        if (req.body.fromDate) {
            options.fromDate = new Date(req.body.fromDate);
        }
        if (req.body.toDate) {
            options.toDate = new Date(req.body.toDate);
        }
        // Limit
        if (req.body.limit) {
            options.limit = parseInt(req.body.limit, 10);
        }
        // Touren synchronisieren
        const result = await komootService_1.default.syncTours(options);
        // Erfolg/Fehler anzeigen
        let message = `Synchronisierung abgeschlossen: ${result.newTours} neue Touren`;
        if (result.updatedTours > 0) {
            message += `, ${result.updatedTours} aktualisierte Touren`;
        }
        if (result.skippedTours > 0) {
            message += `, ${result.skippedTours} übersprungene Touren`;
        }
        if (result.failedTours > 0) {
            message += `, ${result.failedTours} fehlgeschlagene Touren`;
            log.warn(`${fnLogPrefix} ${result.failedTours} tours failed to sync`);
        }
        log.debug(`${fnLogPrefix} Setting flash message: ${message}`);
        // Prüfen, ob es sich um eine AJAX-Anfrage handelt
        if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
            // AJAX-Antwort senden
            return res.json({
                status: 'success',
                message,
                result
            });
        }
        // Flash-Nachricht setzen
        req.flash('success', message);
        // Weiterleitung zur Touren-Seite
        res.redirect('/user/komoot/tours');
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to sync Komoot tours: ${error}`);
        req.flash('error', 'Ein Fehler ist bei der Synchronisierung aufgetreten. Bitte versuchen Sie es später erneut.');
        res.redirect('/user/komoot/tours');
    }
};
exports.syncKomootTours = syncKomootTours;
/**
 * Zeigt die Details einer Komoot-Tour an
 */
const showKomootTour = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl ShowTour User:${req.user?.id} TourID:${req.params.id}]`;
    try {
        const tourId = parseInt(req.params.id, 10);
        if (isNaN(tourId)) {
            req.flash('error', 'Ungültige Tour-ID');
            return res.redirect('/user/komoot/tours');
        }
        // Tour holen
        const tour = await komootService_1.default.getTour(tourId);
        if (!tour) {
            req.flash('error', 'Tour nicht gefunden');
            return res.redirect('/user/komoot/tours');
        }
        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user.id) {
            req.flash('error', 'Sie haben keinen Zugriff auf diese Tour');
            return res.redirect('/user/komoot/tours');
        }
        // Sport-Typen holen
        const sportTypes = await sportTypeRepository.getAllSportTypes();
        // Seite rendern
        res.render('users/komoot_tour_details', {
            pageTitle: `Komoot-Tour: ${tour.name}`,
            tour,
            sportTypes
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to show Komoot tour: ${error}`);
        next(error);
    }
};
exports.showKomootTour = showKomootTour;
/**
 * Zeigt das Formular zur Konvertierung einer Komoot-Tour in eine Aktivität
 */
const showConvertKomootTourForm = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl ShowConvertForm User:${req.user?.id} TourID:${req.params.id}]`;
    try {
        const tourId = parseInt(req.params.id, 10);
        if (isNaN(tourId)) {
            req.flash('error', 'Ungültige Tour-ID');
            return res.redirect('/user/komoot/tours');
        }
        // Tour holen
        const tour = await komootService_1.default.getTour(tourId);
        if (!tour) {
            req.flash('error', 'Tour nicht gefunden');
            return res.redirect('/user/komoot/tours');
        }
        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user.id) {
            req.flash('error', 'Sie haben keinen Zugriff auf diese Tour');
            return res.redirect('/user/komoot/tours');
        }
        // Formular zur Konvertierung anzeigen
        res.render('users/komoot_tour_convert', {
            pageTitle: 'Komoot-Tour konvertieren',
            tour
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to show convert form: ${error}`);
        req.flash('error', 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.');
        res.redirect('/user/komoot/tours');
    }
};
exports.showConvertKomootTourForm = showConvertKomootTourForm;
/**
 * Konvertiert eine Komoot-Tour in eine Aktivität oder geplante Route
 */
const convertKomootTourToActivity = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl ConvertTour User:${req.user?.id} TourID:${req.params.id}]`;
    try {
        const tourId = parseInt(req.params.id, 10);
        if (isNaN(tourId)) {
            req.flash('error', 'Ungültige Tour-ID');
            return res.redirect('/user/komoot/tours');
        }
        // Tour holen
        const tour = await komootService_1.default.getTour(tourId);
        if (!tour) {
            req.flash('error', 'Tour nicht gefunden');
            return res.redirect('/user/komoot/tours');
        }
        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user.id) {
            req.flash('error', 'Sie haben keinen Zugriff auf diese Tour');
            return res.redirect('/user/komoot/tours');
        }
        // Formulardaten holen
        const { activity_name, sport_type, activity_type, private_note, extract_elevation } = req.body;
        // Konvertierung durchführen
        const result = await komootService_1.default.convertTourToActivity({
            tour,
            userId: req.user.id,
            activityName: activity_name,
            sportType: sport_type,
            activityType: activity_type,
            privateNote: private_note,
            extractElevation: extract_elevation === '1'
        });
        if (result.success) {
            req.flash('success', result.message);
            res.redirect('/user/komoot/tours');
        }
        else {
            req.flash('error', result.message || 'Ein Fehler ist bei der Konvertierung aufgetreten.');
            res.redirect(`/user/komoot/tours/${tourId}`);
        }
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to convert Komoot tour to activity: ${error}`);
        req.flash('error', 'Ein Fehler ist bei der Konvertierung aufgetreten. Bitte versuchen Sie es später erneut.');
        res.redirect(`/user/komoot/tours/${req.params.id}`);
    }
};
exports.convertKomootTourToActivity = convertKomootTourToActivity;
/**
 * Zeigt die Synchronisierungsseite an
 */
const showKomootSync = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl ShowSync User:${req.user?.id}]`;
    try {
        // Anmeldeinformationen holen
        const credentials = await komootService_1.default.getCredentials(req.user.id);
        if (!credentials) {
            req.flash('error', 'Keine Komoot-Anmeldeinformationen gefunden. Bitte konfigurieren Sie Ihre Komoot-Einstellungen.');
            return res.redirect('/user/komoot/settings');
        }
        // Seite rendern
        res.render('users/komoot_sync', {
            pageTitle: 'Komoot-Touren synchronisieren'
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to show Komoot sync page: ${error}`);
        next(error);
    }
};
exports.showKomootSync = showKomootSync;
/**
 * Zeigt die Vorschau einer Komoot-Tour an
 */
const showKomootTourPreview = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl ShowTourPreview User:${req.user?.id} TourID:${req.params.id}]`;
    try {
        const tourId = parseInt(req.params.id, 10);
        if (isNaN(tourId)) {
            req.flash('error', 'Ungültige Tour-ID');
            return res.redirect('/user/komoot/tours');
        }
        // Tour holen
        const tour = await komootService_1.default.getTour(tourId);
        if (!tour) {
            req.flash('error', 'Tour nicht gefunden');
            return res.redirect('/user/komoot/tours');
        }
        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user.id) {
            req.flash('error', 'Sie haben keinen Zugriff auf diese Tour');
            return res.redirect('/user/komoot/tours');
        }
        // Sport-Typen holen
        const sportTypes = await sportTypeRepository.getAllSportTypes();
        // Seite rendern
        res.render('users/komoot_tour_preview', {
            pageTitle: `Vorschau: ${tour.name}`,
            tour,
            sportTypes
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to show Komoot tour preview: ${error}`);
        next(error);
    }
};
exports.showKomootTourPreview = showKomootTourPreview;
/**
 * Liefert die GPX-Daten einer Komoot-Tour
 */
const getKomootTourGpx = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl GetTourGpx User:${req.user?.id} TourID:${req.params.id}]`;
    try {
        const tourId = parseInt(req.params.id, 10);
        if (isNaN(tourId)) {
            return res.status(400).json({ success: false, message: 'Ungültige Tour-ID' });
        }
        // Tour holen
        const tour = await komootService_1.default.getTour(tourId);
        if (!tour) {
            return res.status(404).json({ success: false, message: 'Tour nicht gefunden' });
        }
        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user.id) {
            return res.status(403).json({ success: false, message: 'Sie haben keinen Zugriff auf diese Tour' });
        }
        // GPX-Daten holen
        const gpxData = await komootService_1.default.getGpxData(tour);
        if (!gpxData) {
            return res.status(404).json({ success: false, message: 'GPX-Datei nicht gefunden' });
        }
        res.json({ success: true, gpxData });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Failed to get Komoot tour GPX: ${error}`);
        res.status(500).json({ success: false, message: 'Ein Fehler ist aufgetreten' });
    }
};
exports.getKomootTourGpx = getKomootTourGpx;
/**
 * Extrahiert POIs aus einer Komoot-Tour
 */
const getKomootTourPois = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl GetTourPois User:${req.user?.id} TourID:${req.params.id}]`;
    try {
        const tourId = parseInt(req.params.id, 10);
        if (isNaN(tourId)) {
            return res.status(400).json({ success: false, message: 'Ungültige Tour-ID' });
        }
        // Tour holen
        const tour = await komootService_1.default.getTour(tourId);
        if (!tour) {
            return res.status(404).json({ success: false, message: 'Tour nicht gefunden' });
        }
        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user.id) {
            return res.status(403).json({ success: false, message: 'Sie haben keinen Zugriff auf diese Tour' });
        }
        // POIs aus der Tour extrahieren
        const pois = await komootService_1.default.extractPoisFromTour(tour);
        if (!pois) {
            return res.status(404).json({ success: false, message: 'Keine POIs gefunden oder GPX-Datei nicht verfügbar' });
        }
        res.json({
            success: true,
            data: pois
        });
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error extracting POIs: ${error}`);
        res.status(500).json({ success: false, message: 'Interner Serverfehler' });
    }
};
exports.getKomootTourPois = getKomootTourPois;
/**
 * Importiert ausgewählte POIs aus einer Komoot-Tour
 */
const importKomootTourPois = async (req, res, next) => {
    const fnLogPrefix = `[KomootCtrl ImportTourPois User:${req.user?.id} TourID:${req.params.id}]`;
    try {
        const tourId = parseInt(req.params.id, 10);
        if (isNaN(tourId)) {
            req.flash('error', 'Ungültige Tour-ID');
            return res.redirect('/user/komoot/tours');
        }
        // Tour holen
        const tour = await komootService_1.default.getTour(tourId);
        if (!tour) {
            req.flash('error', 'Tour nicht gefunden');
            return res.redirect('/user/komoot/tours');
        }
        // Überprüfen, ob der Benutzer Zugriff auf die Tour hat
        if (tour.user_id !== req.user.id) {
            req.flash('error', 'Sie haben keinen Zugriff auf diese Tour');
            return res.redirect('/user/komoot/tours');
        }
        // Ausgewählte POIs aus dem Request holen
        const { selected_pois } = req.body;
        if (!selected_pois || !Array.isArray(selected_pois)) {
            req.flash('error', 'Keine POIs ausgewählt');
            return res.redirect(`/user/komoot/tours/${tourId}`);
        }
        // POI-Indizes in Zahlen umwandeln
        const selectedPoiIndices = selected_pois.map(index => parseInt(index, 10)).filter(index => !isNaN(index));
        if (selectedPoiIndices.length === 0) {
            req.flash('error', 'Keine gültigen POIs ausgewählt');
            return res.redirect(`/user/komoot/tours/${tourId}`);
        }
        // POIs importieren
        const result = await komootService_1.default.importPoisFromTour(tour, req.user.id, selectedPoiIndices);
        if (result.success) {
            req.flash('success', result.message);
        }
        else {
            req.flash('error', result.message);
        }
        res.redirect(`/user/komoot/tours/${tourId}`);
    }
    catch (error) {
        log.error(`${fnLogPrefix} Error importing POIs: ${error}`);
        req.flash('error', 'Ein Fehler ist beim Import der POIs aufgetreten.');
        res.redirect(`/user/komoot/tours/${req.params.id}`);
    }
};
exports.importKomootTourPois = importKomootTourPois;
//# sourceMappingURL=komootController.js.map